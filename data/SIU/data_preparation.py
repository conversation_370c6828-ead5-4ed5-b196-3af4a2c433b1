#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @File    : data_preparation.py
# @Time    : 2024/07/21
import os
import pickle as pkl
import lmdb
import numpy as np
from multiprocessing import cpu_count
from concurrent.futures import <PERSON><PERSON>oolExecutor
from tqdm import tqdm

# Configuration
PICKLE_FILE       = "./final_dic.pkl"
LMDB_PATH         = "./lmdb_siu"
MAP_SIZE          = 100_995_116_277_76
N_PROC_WORKERS    = min(cpu_count(), 32)
PROC_CHUNK_SIZE   = 100
ERR_FILE          = "./siu_processing_errors.txt"


def process_data_point(args):
    """Process a single data point from the SIU dataset"""
    global_idx, uniprot_id, data_idx, data_point = args

    try:
        # Extract required fields from data_point
        atoms = data_point.get('atoms', [])
        coordinates = data_point.get('coordinates', [])
        pocket_atoms = data_point.get('pocket_atoms', [])
        pocket_coordinates = data_point.get('pocket_coordinates', [])
        source_data = data_point.get('source_data', {})
        label = data_point.get('label', {})
        inchikey = data_point.get('ik', '')
        smiles = data_point.get('smi', '')

        # Validate essential data
        if not atoms or not pocket_atoms:
            return None, f"Missing atoms or pocket_atoms for {uniprot_id}_{data_idx}"

        if not coordinates or not pocket_coordinates:
            return None, f"Missing coordinates for {uniprot_id}_{data_idx}"

        # Handle multiple conformations - use the first one if multiple exist
        if isinstance(coordinates, list) and len(coordinates) > 0:
            if isinstance(coordinates[0], list) and len(coordinates[0]) > 0:
                # Multiple conformations case
                lig_coords = np.array(coordinates[0], dtype=np.float32)
            else:
                # Single conformation case
                lig_coords = np.array(coordinates, dtype=np.float32)
        else:
            return None, f"Invalid coordinates format for {uniprot_id}_{data_idx}"

        # Convert pocket coordinates to numpy array
        pocket_coords = np.array(pocket_coordinates, dtype=np.float32)

        total_atoms = len(lig_coords) + len(pocket_coords)
        if total_atoms > 1500:
            # Prioritize ligand atoms, trim pocket if necessary
            max_pocket_atoms = 1500 - len(lig_coords)
            if max_pocket_atoms > 0:
                pocket_coords = pocket_coords[:max_pocket_atoms]
                pocket_atoms = pocket_atoms[:max_pocket_atoms]
            else:
                return None, f"Too many ligand atoms for {uniprot_id}_{data_idx}"

        # Create data dictionary in format similar to BioLip
        data = {
            'pocket_atoms': pocket_atoms,
            'pocket_coordinates': pocket_coords,
            'lig_atoms_real': atoms,
            'atoms': atoms,
            'lig_coord_real': lig_coords.tolist(),
            'coordinates': lig_coords.tolist(),  # Keep original field name
            'smi': smiles,
            'inchikey': inchikey,
            # 'uniprot_id': uniprot_id,
            'source_data': source_data,
            'label': label
        }

        # Create unique key for this data point (simple sequential numbering)
        key = str(global_idx)

        return key, pkl.dumps(data)

    except Exception as e:
        return None, f"Error processing {uniprot_id}_{data_idx}: {str(e)}"


def load_and_prepare_data(pickle_file):
    """Load the pickle file and prepare data for processing"""
    print(f"Loading data from {pickle_file}...")

    with open(pickle_file, 'rb') as f:
        final_dic = pkl.load(f)

    print(f"Loaded data for {len(final_dic)} UniProt IDs")

    # Flatten the data structure for parallel processing
    all_data_points = []
    global_idx = 0
    for uniprot_id, data_list in final_dic.items():
        for idx, data_point in enumerate(data_list):
            all_data_points.append((global_idx, uniprot_id, idx, data_point))
            global_idx += 1

    print(f"Total data points to process: {len(all_data_points)}")
    return all_data_points


def main():
    """Main processing function"""
    if not os.path.exists(PICKLE_FILE):
        print(f"Error: {PICKLE_FILE} not found!")
        return

    # Create output directories
    os.makedirs(os.path.dirname(LMDB_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(ERR_FILE), exist_ok=True)

    # Load and prepare data
    all_data_points = load_and_prepare_data(PICKLE_FILE)

    if not all_data_points:
        print("No data points to process!")
        return

    # Initialize LMDB environment
    env = lmdb.open(LMDB_PATH, map_size=MAP_SIZE, lock=False)

    successful_count = 0
    error_count = 0

    with env.begin(write=True) as txn, open(ERR_FILE, 'w') as err_f:
        # Process data points in parallel
        with ProcessPoolExecutor(max_workers=N_PROC_WORKERS) as executor:
            results = executor.map(process_data_point,
                                 all_data_points,
                                 chunksize=PROC_CHUNK_SIZE)

            for result in tqdm(results, total=len(all_data_points), desc="Processing SIU data"):
                if result[0] is not None:  # Successful processing
                    key, serialized_data = result
                    txn.put(key.encode(), serialized_data)
                    successful_count += 1
                else:  # Error occurred
                    error_msg = result[1]
                    err_f.write(f"{error_msg}\n")
                    error_count += 1

    env.close()

    print(f"✅ Processing complete!")
    print(f"   Successfully processed: {successful_count} data points")
    print(f"   Errors encountered: {error_count} data points")
    print(f"   LMDB database saved to: {LMDB_PATH}")
    if error_count > 0:
        print(f"   Error details saved to: {ERR_FILE}")


if __name__ == "__main__":
    main()