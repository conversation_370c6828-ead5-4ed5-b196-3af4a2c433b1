# An improved version of the <PERSON> dataset for drug-target affinity (DTA) prediction

This repository provides an improved version of the <PERSON> dataset, which is a benchmark dataset for the prediction of drug-target affinity (DTA). The present dataset was prepared by revising the protein sequence data of a previous version of the <PERSON> dataset from the [DeepDTA paper](https://doi.org/10.1093/bioinformatics/bty593). The [original Davis dataset](https://www.nature.com/articles/nbt.1990) was published by <PERSON> *et al.* in Nature Biotechnology in 2011.

- **drugs.csv** contains the PubChem CIDs and the SMILES strings of 68 drugs in the Davis dataset. Both canonical and isomeric SMILES strings are included. The data was obtained from the [DeepDTA repository](https://github.com/hkmztrk/DeepDTA/tree/master/data/davis).

- **proteins.csv** contains the accession numbers, the gene names and the sequences of 433 kinases in the Davis dataset. We started with all the 442 kinases in the original Davis dataset. The sequences were collected by the accession number from the protein database of NCBI. The sequences of the mutant variants were corrected according to the annotations in the original dataset. The specific domain was picked if it had been specified. Nine records, including six nonphosphorylated ABL1 proteins, two CDK4-Cyclin complexes and one variant of FLT3 (FLT3-ITD), were deleted. In the DeepDTA paper, the sequences were directly used after they were collected by the gene name and the accession number.

- **drug_protein_affinity.csv** lists the affinity score of 29,444 drug-target pairs. The drugs and the proteins are represented by their indices assigned in **drug.csv** and **protein.csv**. We downloaded the affinity data in the DeepDTA repository and transformed it into a tabular format. The records involving the deleted proteins mentioned above were removed. As suggested by the DeepDTA paper, the affinity score, which is the dissociation constant (K<sub>d</sub>), can be converted into pK<sub>d</sub> with the following formula before use.  
*pK<sub>d</sub> = -log<sub>10</sub>(K<sub>d</sub>/1e9)*