# Copyright (c) DP Technology.
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

"""
UniMol Task Module for Protein-Ligand Binding Research

This module implements the UniMolTask class for training transformer auto-encoder models
in protein-ligand binding studies. It supports both standard molecular pretraining and
complex protein-ligand pretraining modes.

Author: DP Technology
License: MIT
"""

import logging
import os
from typing import Dict, Any, Optional, Union

import numpy as np

# Unicore framework imports
from unicore.data import (
    Dictionary,
    NestedDictionaryDataset,
    AppendTokenDataset,
    PrependTokenDataset,
    RightPadDataset,
    EpochShuffleDataset,
    TokenizeDataset,
    RightPadDataset2D,
    FromNumpyDataset,
    RawArrayDataset,
)
from unicore.tasks import UnicoreTask, register_task

# UniMol data processing imports
from unimol.data import (
    KeyDataset,
    ConformerSampleDataset,
    DistanceDataset,
    EdgeTypeDataset,
    MaskPointsDataset,
    RemoveHydrogenDataset,
    AtomTypeDataset,
    NormalizeDataset,
    <PERSON><PERSON><PERSON><PERSON><PERSON>set,
    RightPadDatasetCoord,
    Add2DConformerDataset,
    ExtractCPConformerDataset2,
    LMDBDataset,
    CrossDockData
)

# Configure logger for this module
logger = logging.getLogger(__name__)

# Constants for dataset configuration
DEFAULT_MASK_PROB = 0.15
DEFAULT_LEAVE_UNMASKED_PROB = 0.05
DEFAULT_RANDOM_TOKEN_PROB = 0.05
DEFAULT_NOISE = 1.0
DEFAULT_MAX_ATOMS = 256
DEFAULT_DICT_NAME = "dict_protein.txt"
DEFAULT_LIG_DICT_NAME = "dict_ligand.txt"
DEFAULT_RUN_NAME = "cdmol"


@register_task("unimol")
class UniMolTask(UnicoreTask):
    """
    UniMol Task for Protein-Ligand Binding Research

    This class implements a task for training transformer auto-encoder models
    specifically designed for protein-ligand binding studies. It supports both
    standard molecular pretraining and complex protein-ligand pretraining modes.

    The task handles:
    - Molecular conformer sampling and processing
    - Protein-ligand complex data preparation
    - Masking strategies for self-supervised learning
    - Distance and edge type computation for molecular graphs

    Attributes:
        dictionary: Protein atom type dictionary
        lig_dictionary: Ligand atom type dictionary
        mask_idx: Index for mask tokens
        complex_pretrain: Flag for complex pretraining mode
        online_ligfeat: Flag for online ligand feature computation
    """

    @staticmethod
    def add_args(parser):
        """
        Add task-specific command line arguments.

        Args:
            parser: ArgumentParser instance to add arguments to
        """
        # Data path configuration
        parser.add_argument(
            "data",
            help="Colon separated path to data directories list, "
                 "will be iterated upon during epochs in round-robin manner",
        )

        # Masking strategy parameters
        parser.add_argument(
            "--mask-prob",
            default=DEFAULT_MASK_PROB,
            type=float,
            help="Probability of replacing a token with mask",
        )
        parser.add_argument(
            "--leave-unmasked-prob",
            default=DEFAULT_LEAVE_UNMASKED_PROB,
            type=float,
            help="Probability that a masked token is unmasked",
        )
        parser.add_argument(
            "--random-token-prob",
            default=DEFAULT_RANDOM_TOKEN_PROB,
            type=float,
            help="Probability of replacing a token with a random token",
        )

        # Noise configuration
        parser.add_argument(
            "--noise-type",
            default="uniform",
            choices=["trunc_normal", "uniform", "normal", "none"],
            help="Noise type in coordinate noise",
        )
        parser.add_argument(
            "--noise",
            default=DEFAULT_NOISE,
            type=float,
            help="Coordinate noise for masked atoms",
        )

        # Hydrogen handling options
        parser.add_argument(
            "--remove-hydrogen",
            action="store_true",
            help="Remove hydrogen atoms",
        )
        parser.add_argument(
            "--remove-polar-hydrogen",
            action="store_true",
            help="Remove polar hydrogen atoms",
        )
        parser.add_argument(
            "--only-polar",
            default=1,
            type=int,
            help="1: only polar hydrogen; -1: all hydrogen; 0: remove all hydrogen",
        )

        # Model configuration
        parser.add_argument(
            "--max-atoms",
            type=int,
            default=DEFAULT_MAX_ATOMS,
            help="Selected maximum number of atoms in a molecule",
        )

        # Dictionary files
        parser.add_argument(
            "--dict-name",
            default=DEFAULT_DICT_NAME,
            help="Protein dictionary file",
        )
        parser.add_argument(
            "--ligdict-name",
            default=DEFAULT_LIG_DICT_NAME,
            help="Ligand dictionary file",
        )

        # Experiment configuration
        parser.add_argument(
            "--run-name",
            default=DEFAULT_RUN_NAME,
            type=str,
            help="Wandb run name",
        )
        parser.add_argument(
            "--remove-lba-casf",
            default=0,
            type=int,
            help="Remove the overlap with LBA and CASF datasets",
        )
        parser.add_argument(
            "--gce-std-dev",
            default=1.5,
            type=float,
            help="Standard deviation for atom similarity distribution in GCE loss",
        )

    def __init__(self, args, dictionary, lig_dictionary):
        """
        Initialize UniMolTask with dictionaries and configuration.

        Args:
            args: Configuration arguments
            dictionary: Protein atom type dictionary
            lig_dictionary: Ligand atom type dictionary
        """
        super().__init__(args)
        self.dictionary = dictionary
        self.lig_dictionary = lig_dictionary
        self.seed = args.seed

        # Add mask tokens to both dictionaries
        self.mask_idx = dictionary.add_symbol("[MASK]", is_special=True)
        lig_dictionary.add_symbol("[MASK]", is_special=True)

        # Store configuration flags for easy access
        self.complex_pretrain = getattr(args, 'complex_pretrain', False)
        self.idx_path = getattr(args, 'idx_path', None)
        self.max_comnum = getattr(args, 'max_comnum', DEFAULT_MAX_ATOMS)
        self.online_ligfeat = getattr(args, 'online_ligfeat', False)
        self.rdkit_random = getattr(args, 'rdkit_random', False)
        self.mask_feature = getattr(args, 'mask_feature', False)

        # Configure hydrogen removal strategy
        self._configure_hydrogen_removal(args)

    def _configure_hydrogen_removal(self, args):
        """
        Configure hydrogen removal strategy based on only_polar parameter.

        Args:
            args: Configuration arguments containing only_polar setting
        """
        if args.only_polar > 0:
            args.remove_polar_hydrogen = True
        elif args.only_polar < 0:
            args.remove_polar_hydrogen = False
        else:
            args.remove_hydrogen = True

    @classmethod
    def setup_task(cls, args, **kwargs):
        """
        Set up the UniMol task by loading dictionaries.

        Args:
            args: Configuration arguments
            **kwargs: Additional keyword arguments (unused)

        Returns:
            UniMolTask: Initialized task instance
        """
        # Load protein and ligand dictionaries
        dict_path = os.path.join(args.data, args.dict_name)
        lig_dict_path = os.path.join(args.data, args.ligdict_name)

        dictionary = Dictionary.load(dict_path)
        lig_dictionary = Dictionary.load(lig_dict_path)

        logger.info(f"Loaded protein dictionary with {len(dictionary)} types")
        logger.info(f"Loaded ligand dictionary with {len(lig_dictionary)} types")

        return cls(args, dictionary, lig_dictionary)

    def load_dataset(self, split, combine=False, **kwargs):
        """
        Load a given dataset split for training or evaluation.

        Args:
            split (str): Name of the split (e.g., train, valid, test)
            combine (bool): Whether to combine datasets (unused)
            **kwargs: Additional keyword arguments (unused)
        """
        # Load raw dataset based on pretraining mode
        raw_dataset = self._load_raw_dataset(split)


        # Create processed dataset
        net_input, target = self._create_processed_dataset(
            raw_dataset,
            coord_seed=self.args.seed,
            mask_seed=self.args.seed
        )

        # Combine into nested dictionary dataset
        dataset = NestedDictionaryDataset({
            "net_input": net_input,
            "target": target
        })
        
        # Apply epoch shuffling for training splits
        if split in ["train", "train.small"]:
            dataset = EpochShuffleDataset(dataset, len(dataset), self.args.seed)

        self.datasets[split] = dataset

    def _load_raw_dataset(self, split):
        """
        Load the raw dataset for the given split.

        Args:
            split (str): Dataset split name

        Returns:
            Dataset: Raw dataset instance
        """
        if self.complex_pretrain:
            dataset_path = f'{self.args.data}/{split}.lmdb'
            return CrossDockData(dataset_path, max_num=self.max_comnum)
        else:
            split_path = os.path.join(self.args.data, f"{split}.lmdb")
            return LMDBDataset(split_path)

    def _create_processed_dataset(self, raw_dataset, coord_seed, mask_seed):
        """
        Create processed dataset with all transformations applied.

        Args:
            raw_dataset: Raw input dataset
            coord_seed: Seed for coordinate sampling
            mask_seed: Seed for masking operations

        Returns:
            tuple: (net_input, target) dictionaries
        """
        # Apply initial dataset transformations
        dataset = self._apply_initial_transforms(raw_dataset, coord_seed)
        # Apply cropping and normalization
        dataset = self._apply_cropping_and_normalization(dataset)
        # Create token and coordinate datasets
        token_dataset, coord_dataset = self._create_token_coord_datasets(dataset)
        # Apply masking
        expand_dataset = self._apply_masking(token_dataset, coord_dataset, mask_seed)
        # Build network input and target
        net_input = self._build_network_input(expand_dataset, dataset)
        target = self._build_target(expand_dataset, dataset)
        return net_input, target

    def _apply_initial_transforms(self, raw_dataset, coord_seed):
        """
        Apply initial dataset transformations based on training mode.

        This method handles the initial processing of molecular data, including:
        - Conformer generation for complex pretraining
        - 2D conformer addition for standard pretraining
        - Hydrogen removal and atom type processing

        Args:
            raw_dataset: Input dataset to transform
            coord_seed: Random seed for coordinate sampling

        Returns:
            Dataset: Transformed dataset ready for further processing
        """
        dataset = raw_dataset

        # Apply training-specific transformations
        if hasattr(self.args, 'mode') and self.args.mode == 'train':
            if self.complex_pretrain:
                # Complex pretraining: extract protein-ligand conformers
                dataset = ExtractCPConformerDataset2(
                    raw_dataset, "smi", "atoms", "coordinates",
                    rdkit_random=self.rdkit_random,
                    mask_feat=self.mask_feature
                )
            else:
                # Standard pretraining: add 2D conformers
                dataset = Add2DConformerDataset(
                    raw_dataset, "smi", "atoms", "coordinates"
                )

        # Apply molecular preprocessing for non-complex pretraining
        if not self.complex_pretrain:
            # Sample conformers and process atom types
            dataset = ConformerSampleDataset(
                dataset, coord_seed, "atoms", "coordinates"
            )
            dataset = AtomTypeDataset(raw_dataset, dataset)

            # Remove hydrogen atoms based on configuration
            dataset = RemoveHydrogenDataset(
                dataset,
                "atoms",
                "coordinates",
                self.args.remove_hydrogen,
                self.args.remove_polar_hydrogen,
            )

        return dataset

    def _apply_cropping_and_normalization(self, dataset):
        """
        Apply cropping and coordinate normalization to the dataset.

        This method ensures that molecular structures are within size limits
        and coordinates are properly normalized for training stability.

        Args:
            dataset: Input dataset to process

        Returns:
            Dataset: Processed dataset with cropping and normalization applied
        """
        # Determine maximum atoms based on pretraining mode
        max_atoms = self.max_comnum if self.complex_pretrain else self.args.max_atoms

        # Apply random cropping to limit molecular size
        dataset = CroppingDataset(
            dataset, self.seed, "atoms", "coordinates", max_atoms
        )

        # Normalize coordinates to zero mean for training stability
        dataset = NormalizeDataset(dataset, "coordinates", normalize_coord=True)
        dataset = NormalizeDataset(dataset, "atoms_pos_lig", normalize_coord=True)
        dataset = NormalizeDataset(dataset, "atoms_pos_lig_solvent", normalize_coord=True)

        # Normalize original ligand coordinates if masking features
        if self.mask_feature:
            dataset = NormalizeDataset(dataset, "atoms_pos_lig_org", normalize_coord=True)
            dataset = NormalizeDataset(dataset, "atoms_pos_lig_solvent_org", normalize_coord=True)

        return dataset

    def _create_token_coord_datasets(self, dataset):
        """
        Create tokenized atom and coordinate datasets.

        Args:
            dataset: Input dataset containing atoms and coordinates

        Returns:
            tuple: (token_dataset, coord_dataset) for further processing
        """
        # Tokenize atom types using the protein dictionary
        token_dataset = KeyDataset(dataset, "atoms")
        token_dataset = TokenizeDataset(
            token_dataset, self.dictionary, max_seq_len=self.args.max_seq_len
        )

        # Extract coordinate information
        coord_dataset = KeyDataset(dataset, "coordinates")

        return token_dataset, coord_dataset

    def _apply_masking(self, token_dataset, coord_dataset, mask_seed, coord_key="coordinates"):
        """
        Apply masking strategy for self-supervised learning.

        This implements the core masking strategy used in UniMol for learning
        molecular representations through masked language modeling.

        Args:
            token_dataset: Tokenized atom dataset
            coord_dataset: Coordinate dataset
            mask_seed: Random seed for reproducible masking

        Returns:
            MaskPointsDataset: Dataset with applied masking strategy
        """
        # Disable masking for complex pretraining to preserve structure
        mask_forbidden = self.complex_pretrain

        return MaskPointsDataset(
            token_dataset,
            coord_dataset,
            self.dictionary,
            pad_idx=self.dictionary.pad(),
            mask_idx=self.mask_idx,
            noise_type=self.args.noise_type,
            noise=self.args.noise,
            seed=mask_seed,
            mask_prob=self.args.mask_prob,
            leave_unmasked_prob=self.args.leave_unmasked_prob,
            random_token_prob=self.args.random_token_prob,
            mask_forbidden=mask_forbidden,
            coord_key=coord_key
        )

    def _build_network_input(self, expand_dataset, dataset):
        """Build the network input dictionary."""
        # Helper function for prepending and appending tokens
        def prepend_and_append(dataset, pre_token, app_token):
            dataset = PrependTokenDataset(dataset, pre_token)
            return AppendTokenDataset(dataset, app_token)

        # Extract basic datasets
        encoder_token_dataset = KeyDataset(expand_dataset, "atoms")
        encoder_coord_dataset = KeyDataset(expand_dataset, "coordinates")

        src_dataset = prepend_and_append(
            encoder_token_dataset, self.dictionary.bos(), self.dictionary.eos()
        )

        # Prepare coordinate dataset
        encoder_coord_dataset = prepend_and_append(encoder_coord_dataset, 0.0, 0.0)
        encoder_distance_dataset = DistanceDataset(encoder_coord_dataset)

        # Create edge type dataset
        edge_type = EdgeTypeDataset(src_dataset, len(self.dictionary))

        # Build base network input
        net_input = {
            "src_tokens": RightPadDataset(src_dataset, pad_idx=self.dictionary.pad()),
            "src_coord": RightPadDatasetCoord(encoder_coord_dataset, pad_idx=0),
            "src_distance": RightPadDataset2D(encoder_distance_dataset, pad_idx=0),
            "src_edge_type": RightPadDataset2D(edge_type, pad_idx=0),
        }

        # Add ligand features if enabled
        if self.online_ligfeat:
            self._add_ligand_features(net_input, dataset, prepend_and_append)
            self._add_solvent_features(net_input, dataset, prepend_and_append)

        # Add complex pretraining specific features
        if self.complex_pretrain:
            self._add_complex_features(net_input, dataset)

        return net_input

    def _add_ligand_features(self, net_input, dataset, prepend_and_append):
        """Add ligand-specific features to network input."""
        # Process ligand tokens
        lig_encoder_token_dataset = KeyDataset(dataset, "atoms_lig")
        lig_encoder_token_dataset = TokenizeDataset(
            lig_encoder_token_dataset, self.lig_dictionary, max_seq_len=self.args.max_seq_len
        )
        lig_src_dataset = prepend_and_append(
            lig_encoder_token_dataset, self.lig_dictionary.bos(), self.lig_dictionary.eos()
        )
        net_input['lig_tokens'] = RightPadDataset(
            lig_src_dataset, pad_idx=self.lig_dictionary.pad()
        )

        # Process ligand coordinates
        lig_encoder_coord_dataset = KeyDataset(dataset, "atoms_pos_lig")
        lig_encoder_coord_dataset = prepend_and_append(lig_encoder_coord_dataset, 0.0, 0.0)
        net_input['lig_coord'] = RightPadDatasetCoord(lig_encoder_coord_dataset, pad_idx=0)

        # Process ligand distances and edge types
        lig_encoder_distance_dataset = DistanceDataset(lig_encoder_coord_dataset)
        lig_edge_type = EdgeTypeDataset(lig_src_dataset, len(self.lig_dictionary))

        net_input['lig_distance'] = RightPadDataset2D(lig_encoder_distance_dataset, pad_idx=0)
        net_input['lig_edge_type'] = RightPadDataset2D(lig_edge_type, pad_idx=0)

        # Add masked feature information if enabled
        if self.mask_feature:
            # Original ligand coordinates
            lig_encoder_coord_dataset_org = KeyDataset(dataset, "atoms_pos_lig_org")
            lig_encoder_coord_dataset_org = prepend_and_append(lig_encoder_coord_dataset_org, 0.0, 0.0)
            net_input['lig_org_coord'] = RightPadDatasetCoord(lig_encoder_coord_dataset_org, pad_idx=0)

            # Original ligand distances
            lig_encoder_distance_dataset_org = DistanceDataset(lig_encoder_coord_dataset_org)
            net_input['lig_org_distance'] = RightPadDataset2D(lig_encoder_distance_dataset_org, pad_idx=0)

            # Feature masking indices
            feat_masking_idx = KeyDataset(dataset, "mask_array")
            feat_masking_idx = prepend_and_append(feat_masking_idx, 0, 0)
            net_input['feat_masking_idx'] = RightPadDataset(feat_masking_idx, pad_idx=0)

    def _add_solvent_features(self, net_input, dataset, prepend_and_append):
        """Add solvent-aware features to network input."""
        # Process solvent-aware ligand coordinates
        lig_encoder_coord_dataset = KeyDataset(dataset, "atoms_pos_lig_solvent")
        lig_encoder_coord_dataset = prepend_and_append(lig_encoder_coord_dataset, 0.0, 0.0)
        net_input['solvent_lig_coord'] = RightPadDatasetCoord(lig_encoder_coord_dataset, pad_idx=0)
        # Process ligand distances and edge types
        lig_encoder_distance_dataset = DistanceDataset(lig_encoder_coord_dataset)

        net_input['solvent_lig_distance'] = RightPadDataset2D(lig_encoder_distance_dataset, pad_idx=0)

        # Add masked feature information if enabled
        if self.mask_feature:
            lig_encoder_coord_dataset_org = KeyDataset(dataset, "atoms_pos_lig_solvent_org")
            lig_encoder_coord_dataset_org = prepend_and_append(lig_encoder_coord_dataset_org, 0.0, 0.0)
            net_input['solvent_lig_org_coord'] = RightPadDatasetCoord(lig_encoder_coord_dataset_org, pad_idx=0)

            # Original ligand distances
            lig_encoder_distance_dataset_org = DistanceDataset(lig_encoder_coord_dataset_org)
            net_input['solvent_lig_org_distance'] = RightPadDataset2D(lig_encoder_distance_dataset_org, pad_idx=0)




    def _add_complex_features(self, net_input, dataset):
        """Add complex pretraining specific features."""
        net_input['prot_num_lst'] = KeyDataset(dataset, "prot_num")
        net_input['lig_num_lst'] = KeyDataset(dataset, "lig_num")



    def _build_target(self, expand_dataset, dataset):
        """Build the target dictionary for training."""
        if self.complex_pretrain:
            # Complex pretraining targets
            prot_pos_dataset = KeyDataset(dataset, "all_coordinate")
            prot_num_dataset = KeyDataset(dataset, "prot_num")
            lig_num_dataset = KeyDataset(dataset, "lig_num")

            return {
                "all_pos": RightPadDatasetCoord(prot_pos_dataset, pad_idx=0),
                "prot_num": prot_num_dataset,
                "lig_num": lig_num_dataset
            }
        else:
            # Standard pretraining targets
            def prepend_and_append(dataset, pre_token, app_token):
                dataset = PrependTokenDataset(dataset, pre_token)
                return AppendTokenDataset(dataset, app_token)

            # Prepare target datasets
            encoder_target_dataset = KeyDataset(expand_dataset, "targets")
            tgt_dataset = prepend_and_append(
                encoder_target_dataset, self.dictionary.pad(), self.dictionary.pad()
            )

            # Prepare coordinate dataset for targets
            coord_dataset = KeyDataset(dataset, "coordinates")
            coord_dataset = FromNumpyDataset(coord_dataset)
            coord_dataset = prepend_and_append(coord_dataset, 0.0, 0.0)
            distance_dataset = DistanceDataset(coord_dataset)


            solvent_lig_coord_dataset = KeyDataset(dataset, "atoms_pos_lig_solvent")
            solvent_lig_coord_dataset = FromNumpyDataset(solvent_lig_coord_dataset)
            solvent_lig_coord_dataset = prepend_and_append(solvent_lig_coord_dataset, 0.0, 0.0)

            # Get SMI dataset for tracking
            smi_dataset = KeyDataset(dataset, "smi")

            return {
                "tokens_target": RightPadDataset(tgt_dataset, pad_idx=self.dictionary.pad()),
                "distance_target": RightPadDataset2D(distance_dataset, pad_idx=0),
                "coord_target": RightPadDatasetCoord(coord_dataset, pad_idx=0),
                "solvent_lig_coord_target": RightPadDatasetCoord(solvent_lig_coord_dataset, pad_idx=0),
                "smi_name": RawArrayDataset(smi_dataset),
            }

    def build_model(self, args):
        """
        Build the model for this task.

        Args:
            args: Configuration arguments

        Returns:
            Model: Built model instance
        """
        from unicore import models

        model = models.build_model(args, self)
        logger.info(f"Built model with {sum(p.numel() for p in model.parameters())} parameters")
        return model
