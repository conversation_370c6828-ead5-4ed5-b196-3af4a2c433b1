# Copyright (c) DP Technology.
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

import logging

import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
from unicore import utils
from unicore.models import BaseUnicoreModel, register_model, register_model_architecture
from unicore.data import Dictionary
from .unimol import UniMolModel, base_architecture, NonLinearHead
from unicore.modules import LayerNorm
from .transformer_encoder_with_pair import TransformerEncoderWithPair
import numpy as np
from unicore.modules import TransformerEncoderLayer

logger = logging.getLogger(__name__)


@register_model("docking_pose")
class DockingPoseModel(BaseUnicoreModel):
    @staticmethod
    def add_args(parser):
        """Add model-specific arguments to the parser."""
        parser.add_argument(
            "--mol-pooler-dropout",
            type=float,
            metavar="D",
            help="dropout probability in the masked_lm pooler layers",
        )
        parser.add_argument(
            "--pocket-pooler-dropout",
            type=float,
            metavar="D",
            help="dropout probability in the masked_lm pooler layers",
        )
        parser.add_argument(
            "--pocket-encoder-layers",
            type=int,
            help="pocket encoder layers",
        )
        parser.add_argument(
            "--recycling",
            type=int,
            default=1,
            help="recycling nums of decoder",
        )
        
        parser.add_argument(
            "--nt-net",
            type=int,
            default=0,
            help="normal transformer layer",
        )
        
        parser.add_argument(
            "--complex-layernum",
            type=int,
            default=12,
            help='',
        )

    def __init__(self, args, mol_dictionary, pocket_dictionary):
        super().__init__()
        unimol_docking_architecture(args)

        self.args = args
        self.mol_model = UniMolModel(args.mol, mol_dictionary)
        self.pocket_model = UniMolModel(args.pocket, pocket_dictionary)
        
        
        if self.args.nt_net:
            self.complex_layernum = args.complex_layernum
            self.complex_layers = nn.ModuleList(
                [
                    TransformerEncoderLayer(
                        embed_dim=args.pocket.encoder_embed_dim,
                        ffn_embed_dim=args.pocket.encoder_ffn_embed_dim,
                        attention_heads=args.pocket.encoder_attention_heads,
                        dropout=args.pocket.emb_dropout,
                        attention_dropout=args.pocket.dropout,
                        activation_dropout=args.pocket.attention_dropout,
                        activation_fn=args.pocket.activation_fn,
                        post_ln=False,
                    )
                    for _ in range(args.complex_layernum)
                ]
            )
            self.cross_distance_project = NonLinearHead(
                args.mol.encoder_embed_dim * 2, 1, "relu"
                )
            self.holo_distance_project = DistanceHead(
                args.mol.encoder_embed_dim * 2, "relu"
            )
        else:
            self.concat_decoder = TransformerEncoderWithPair(
                encoder_layers=4,
                embed_dim=args.mol.encoder_embed_dim,
                ffn_embed_dim=args.mol.encoder_ffn_embed_dim,
                attention_heads=args.mol.encoder_attention_heads,
                emb_dropout=0.1,
                dropout=0.1,
                attention_dropout=0.1,
                activation_dropout=0.0,
                activation_fn="gelu",
            )
            self.cross_distance_project = NonLinearHead(
                args.mol.encoder_embed_dim * 2 + args.mol.encoder_attention_heads, 1, "relu"
            )
            self.holo_distance_project = DistanceHead(
                args.mol.encoder_embed_dim + args.mol.encoder_attention_heads, "relu"
            )

    @classmethod
    def build_model(cls, args, task):
        """Build a new model instance."""
        return cls(args, task.dictionary, task.pocket_dictionary)

    def forward(
        self,
        mol_src_tokens,
        mol_src_distance,
        mol_src_edge_type,
        pocket_src_tokens,
        pocket_src_distance,
        pocket_src_edge_type,
        masked_tokens=None,
        features_only=True,
        **kwargs
    ):
        def get_dist_features(dist, et, flag):
            if flag == "mol":
                n_node = dist.size(-1)
                gbf_feature = self.mol_model.gbf(dist, et)
                gbf_result = self.mol_model.gbf_proj(gbf_feature)
                graph_attn_bias = gbf_result
                graph_attn_bias = graph_attn_bias.permute(0, 3, 1, 2).contiguous()
                graph_attn_bias = graph_attn_bias.view(-1, n_node, n_node)
                return graph_attn_bias
            else:
                n_node = dist.size(-1)
                gbf_feature = self.pocket_model.gbf(dist, et)
                gbf_result = self.pocket_model.gbf_proj(gbf_feature)
                graph_attn_bias = gbf_result
                graph_attn_bias = graph_attn_bias.permute(0, 3, 1, 2).contiguous()
                graph_attn_bias = graph_attn_bias.view(-1, n_node, n_node)
                return graph_attn_bias

        if self.args.freeze_encoder:
            with torch.no_grad():
                mol_padding_mask = mol_src_tokens.eq(self.mol_model.padding_idx)
                mol_x = self.mol_model.embed_tokens(mol_src_tokens)
                mol_graph_attn_bias = get_dist_features(
                    mol_src_distance, mol_src_edge_type, "mol"
                )
                mol_outputs = self.mol_model.encoder(
                    mol_x, padding_mask=mol_padding_mask, attn_mask=mol_graph_attn_bias
                )
                mol_encoder_rep = mol_outputs[0]
                mol_encoder_pair_rep = mol_outputs[1]

                pocket_padding_mask = pocket_src_tokens.eq(self.pocket_model.padding_idx)
                pocket_x = self.pocket_model.embed_tokens(pocket_src_tokens)
                pocket_graph_attn_bias = get_dist_features(
                    pocket_src_distance, pocket_src_edge_type, "pocket"
                )
                pocket_outputs = self.pocket_model.encoder(
                    pocket_x, padding_mask=pocket_padding_mask, attn_mask=pocket_graph_attn_bias
                )
                pocket_encoder_rep = pocket_outputs[0]
                pocket_encoder_pair_rep = pocket_outputs[1]

                mol_sz = mol_encoder_rep.size(1)
                pocket_sz = pocket_encoder_rep.size(1)
        else:
            mol_padding_mask = mol_src_tokens.eq(self.mol_model.padding_idx)
            mol_x = self.mol_model.embed_tokens(mol_src_tokens)
            mol_graph_attn_bias = get_dist_features(
                mol_src_distance, mol_src_edge_type, "mol"
            )
            mol_outputs = self.mol_model.encoder(
                mol_x, padding_mask=mol_padding_mask, attn_mask=mol_graph_attn_bias
            )
            mol_encoder_rep = mol_outputs[0]
            mol_encoder_pair_rep = mol_outputs[1]

            pocket_padding_mask = pocket_src_tokens.eq(self.pocket_model.padding_idx)
            pocket_x = self.pocket_model.embed_tokens(pocket_src_tokens)
            pocket_graph_attn_bias = get_dist_features(
                pocket_src_distance, pocket_src_edge_type, "pocket"
            )
            pocket_outputs = self.pocket_model.encoder(
                pocket_x, padding_mask=pocket_padding_mask, attn_mask=pocket_graph_attn_bias
            )
            pocket_encoder_rep = pocket_outputs[0]
            pocket_encoder_pair_rep = pocket_outputs[1]

            mol_sz = mol_encoder_rep.size(1)
            pocket_sz = pocket_encoder_rep.size(1)
            
        
        if self.args.nt_net:
            # concat to get pairwise embeddings
            all_padding_mask = torch.cat([pocket_padding_mask, mol_padding_mask], dim=1)
            all_feat_x = torch.cat([pocket_encoder_rep, mol_encoder_rep], dim=1)
            for i in range(len(self.complex_layers)):
                all_feat_x = self.complex_layers[i](
                            all_feat_x, padding_mask=all_padding_mask
                        )
            
            pocket_encoder_rep = all_feat_x[:, :pocket_sz, :]
            mol_encoder_rep = all_feat_x[:,pocket_sz:,:]

            
            
            mol_left_pad = mol_encoder_rep.unsqueeze(2).repeat(1, 1, mol_sz, 1)
            mol_right_pad = mol_encoder_rep.unsqueeze(1).repeat(1, mol_sz, 1, 1)
            holo_encoder_pair_rep = torch.cat([mol_left_pad, mol_right_pad], dim=3)
            holo_distance_predict = self.holo_distance_project(
                holo_encoder_pair_rep
            )  # batch, mol_sz, mol_sz
            
            mol_poc_left_pad = mol_encoder_rep.unsqueeze(2).repeat(1, 1, pocket_sz, 1)
            poc_right_pad = pocket_encoder_rep.unsqueeze(1).repeat(1, mol_sz, 1, 1)
            cross_rep = torch.cat([mol_poc_left_pad, poc_right_pad], dim=3)
            cross_distance_predict = (
                F.elu(self.cross_distance_project(cross_rep).squeeze(-1)) + 1.0
            )  # batch, mol_sz, pocket_sz
        else:
            concat_rep = torch.cat(
                [mol_encoder_rep, pocket_encoder_rep], dim=-2
            )  # [batch, mol_sz+pocket_sz, hidden_dim]
            concat_mask = torch.cat(
                [mol_padding_mask, pocket_padding_mask], dim=-1
            )  # [batch, mol_sz+pocket_sz]
            attn_bs = mol_graph_attn_bias.size(0)

            concat_attn_bias = torch.zeros(
                attn_bs, mol_sz + pocket_sz, mol_sz + pocket_sz
            ).type_as(
                concat_rep
            )  # [batch, mol_sz+pocket_sz, mol_sz+pocket_sz]
            concat_attn_bias[:, :mol_sz, :mol_sz] = (
                mol_encoder_pair_rep.permute(0, 3, 1, 2)
                .reshape(-1, mol_sz, mol_sz)
                .contiguous()
            )
            concat_attn_bias[:, -pocket_sz:, -pocket_sz:] = (
                pocket_encoder_pair_rep.permute(0, 3, 1, 2)
                .reshape(-1, pocket_sz, pocket_sz)
                .contiguous()
            )

            decoder_rep = concat_rep
            decoder_pair_rep = concat_attn_bias
            for i in range(self.args.recycling):
                decoder_outputs = self.concat_decoder(
                    decoder_rep, padding_mask=concat_mask, attn_mask=decoder_pair_rep
                )
                decoder_rep = decoder_outputs[0]
                decoder_pair_rep = decoder_outputs[1]
                if i != (self.args.recycling - 1):
                    decoder_pair_rep = decoder_pair_rep.permute(0, 3, 1, 2).reshape(
                        -1, mol_sz + pocket_sz, mol_sz + pocket_sz
                    )

            mol_decoder = decoder_rep[:, :mol_sz]
            pocket_decoder = decoder_rep[:, mol_sz:]

            mol_pair_decoder_rep = decoder_pair_rep[:, :mol_sz, :mol_sz, :]
            mol_pocket_pair_decoder_rep = (
                decoder_pair_rep[:, :mol_sz, mol_sz:, :]
                + decoder_pair_rep[:, mol_sz:, :mol_sz, :].transpose(1, 2)
            ) / 2.0
            mol_pocket_pair_decoder_rep[mol_pocket_pair_decoder_rep == float("-inf")] = 0

            cross_rep = torch.cat(
                [
                    mol_pocket_pair_decoder_rep,
                    mol_decoder.unsqueeze(-2).repeat(1, 1, pocket_sz, 1),
                    pocket_decoder.unsqueeze(-3).repeat(1, mol_sz, 1, 1),
                ],
                dim=-1,
            )  # [batch, mol_sz, pocket_sz, 4*hidden_size]

            cross_distance_predict = (
                F.elu(self.cross_distance_project(cross_rep).squeeze(-1)) + 1.0
            )  # batch, mol_sz, pocket_sz

            holo_encoder_pair_rep = torch.cat(
                [
                    mol_pair_decoder_rep,
                    mol_decoder.unsqueeze(-2).repeat(1, 1, mol_sz, 1),
                ],
                dim=-1,
            )  # [batch, mol_sz, mol_sz, 3*hidden_size]
            holo_distance_predict = self.holo_distance_project(
                holo_encoder_pair_rep
            )  # batch, mol_sz, mol_sz

        return cross_distance_predict, holo_distance_predict

    def set_num_updates(self, num_updates):
        """State from trainer to pass along to model at every update."""

        self._num_updates = num_updates

    def get_num_updates(self):
        return self._num_updates


class DistanceHead(nn.Module):
    def __init__(
        self,
        heads,
        activation_fn,
    ):
        super().__init__()
        self.dense = nn.Linear(heads, heads)
        self.layer_norm = nn.LayerNorm(heads)
        self.out_proj = nn.Linear(heads, 1)
        self.activation_fn = utils.get_activation_fn(activation_fn)

    def forward(self, x):
        bsz, seq_len, seq_len, _ = x.size()
        x[x == float("-inf")] = 0
        x = self.dense(x)
        x = self.activation_fn(x)
        x = self.layer_norm(x)
        x = self.out_proj(x).view(bsz, seq_len, seq_len)
        x = (x + x.transpose(-1, -2)) * 0.5
        return x


@register_model_architecture("docking_pose", "docking_pose")
def unimol_docking_architecture(args):

    parser = argparse.ArgumentParser()
    args.mol = parser.parse_args([])
    args.pocket = parser.parse_args([])

    args.mol.encoder_layers = getattr(args, "mol_encoder_layers", 15)
    args.mol.encoder_embed_dim = getattr(args, "mol_encoder_embed_dim", 512)
    args.mol.encoder_ffn_embed_dim = getattr(args, "mol_encoder_ffn_embed_dim", 2048)
    args.mol.encoder_attention_heads = getattr(args, "mol_encoder_attention_heads", 64)
    args.mol.dropout = getattr(args, "mol_dropout", 0.1)
    args.mol.emb_dropout = getattr(args, "mol_emb_dropout", 0.1)
    args.mol.attention_dropout = getattr(args, "mol_attention_dropout", 0.1)
    args.mol.activation_dropout = getattr(args, "mol_activation_dropout", 0.0)
    args.mol.pooler_dropout = getattr(args, "mol_pooler_dropout", 0.0)
    args.mol.max_seq_len = getattr(args, "mol_max_seq_len", 512)
    args.mol.activation_fn = getattr(args, "mol_activation_fn", "gelu")
    args.mol.pooler_activation_fn = getattr(args, "mol_pooler_activation_fn", "tanh")
    args.mol.post_ln = getattr(args, "mol_post_ln", False)
    args.mol.masked_token_loss = -1.0
    args.mol.masked_coord_loss = -1.0
    args.mol.masked_dist_loss = -1.0
    args.mol.x_norm_loss = -1.0
    args.mol.delta_pair_repr_norm_loss = -1.0

    args.pocket.encoder_layers = getattr(args, "pocket_encoder_layers", 15)
    args.pocket.encoder_embed_dim = getattr(args, "pocket_encoder_embed_dim", 512)
    args.pocket.encoder_ffn_embed_dim = getattr(
        args, "pocket_encoder_ffn_embed_dim", 2048
    )
    args.pocket.encoder_attention_heads = getattr(
        args, "pocket_encoder_attention_heads", 64
    )
    args.pocket.dropout = getattr(args, "pocket_dropout", 0.1)
    args.pocket.emb_dropout = getattr(args, "pocket_emb_dropout", 0.1)
    args.pocket.attention_dropout = getattr(args, "pocket_attention_dropout", 0.1)
    args.pocket.activation_dropout = getattr(args, "pocket_activation_dropout", 0.0)
    args.pocket.pooler_dropout = getattr(args, "pocket_pooler_dropout", 0.0)
    args.pocket.max_seq_len = getattr(args, "pocket_max_seq_len", 512)
    args.pocket.activation_fn = getattr(args, "pocket_activation_fn", "gelu")
    args.pocket.pooler_activation_fn = getattr(
        args, "pocket_pooler_activation_fn", "tanh"
    )
    args.pocket.post_ln = getattr(args, "pocket_post_ln", False)
    args.pocket.masked_token_loss = -1.0
    args.pocket.masked_coord_loss = -1.0
    args.pocket.masked_dist_loss = -1.0
    args.pocket.x_norm_loss = -1.0
    args.pocket.delta_pair_repr_norm_loss = -1.0

    base_architecture(args)
