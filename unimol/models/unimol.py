# Copyright (c) DP Technology.
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.
import logging
import math
from typing import Dict, Any, List, Optional, Tuple, Union

import torch
import torch.nn as nn
import torch.nn.functional as F

from unicore import utils
from unicore.models import BaseUnicoreModel, register_model, register_model_architecture
from unicore.modules import LayerNorm, init_bert_params, TransformerEncoderLayer
from .transformer_encoder_with_pair import TransformerEncoderWithPair

# Configure logger for this module
logger = logging.getLogger(__name__)

# Constants for model configuration
DEFAULT_ENCODER_LAYERS = 15
DEFAULT_EMBED_DIM = 512
DEFAULT_FFN_EMBED_DIM = 2048
DEFAULT_ATTENTION_HEADS = 64
DEFAULT_MAX_SEQ_LEN = 512
DEFAULT_DROPOUT = 0.1
DEFAULT_GAUSSIAN_K = 128

class SolventPropertyEncoder(nn.Module):

    def __init__(self, embed_dim: int = 512, dropout: float = 0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.property_projection = nn.Sequential(
            nn.Linear(3, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim)
        )

    def forward(self, solvent_properties: dict) -> torch.Tensor:
        if solvent_properties:
            # Extract properties with strict clamping to prevent extreme values
            mw = torch.clamp(solvent_properties['MW'], min=10.0, max=500.0)
            density = torch.clamp(solvent_properties['density'], min=0.1, max=5.0)
            dielectric = torch.clamp(solvent_properties['dielectric'], min=1.0, max=100.0)

            # Ensure all properties are 1D tensors (flatten if needed)
            mw = mw.flatten()
            density = density.flatten()
            dielectric = dielectric.flatten()

            # Normalize to safe ranges to prevent gradient explosion
            mw_norm = (mw - 100.0) / 100.0  # Center around 0
            density_norm = (density - 1.0) / 1.0  # Center around 0
            dielectric_norm = (dielectric - 25.0) / 25.0  # Center around 0

            # Further clamp normalized values
            mw_norm = torch.clamp(mw_norm, min=-2.0, max=2.0)
            density_norm = torch.clamp(density_norm, min=-2.0, max=2.0)
            dielectric_norm = torch.clamp(dielectric_norm, min=-2.0, max=2.0)

            # Stack the 3 properties to create [batch_size, 3]
            properties = torch.stack([mw_norm, density_norm, dielectric_norm], dim=-1)
            properties = torch.where(torch.isfinite(properties), properties, torch.zeros_like(properties))

            # Project to full embedding dimension
            # Ensure properties tensor has the same dtype as the projection weights
            properties = properties.to(self.property_projection[0].weight.dtype)
            output = self.property_projection(properties)
        else:
            # Return zero tensor with correct embedding dimension and dtype
            batch_size = 1  # Default batch size when no properties
            output = torch.zeros(batch_size, self.embed_dim, dtype=self.property_projection[0].weight.dtype)
        return output

class SolventCrossAttentionLayer(nn.Module):
    """
    Single layer of cross-attention between solvent embeddings and molecular representations.
    """

    def __init__(self, embed_dim, num_heads=8, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads

        # Cross-attention: solvent embeddings attend to molecular representations
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim * 2, embed_dim)
        )

        # Layer normalization
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, mol_representations, solvent_queries):
        """
        Args:
            mol_representations: [batch_size, seq_len, embed_dim] - molecular representations
            solvent_queries: [batch_size, seq_len, embed_dim] - solvent queries
        Returns:
            [batch_size, seq_len, embed_dim] - enhanced molecular representations
        """
        # Cross-attention with residual connection
        solvent_queries = solvent_queries.to(mol_representations.dtype)
        attended, _ = self.cross_attention(
            query=solvent_queries,
            key=mol_representations,
            value=mol_representations
        )
        mol_enhanced = self.norm1(mol_representations + self.dropout(attended))

        # Feed-forward with residual connection
        ffn_out = self.ffn(mol_enhanced)
        mol_enhanced = self.norm2(mol_enhanced + self.dropout(ffn_out))

        return mol_enhanced


class SolventCrossAttentionTransformer(nn.Module):
    """
    Cross Attention Transformer for solvent-aware molecular reconstruction.
    """

    def __init__(self, embed_dim=512, num_heads=8, num_layers=4, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_layers = num_layers

        # Coordinate embedding projection
        self.coord_proj = nn.Linear(3, embed_dim)

        self.layers = nn.ModuleList([
            SolventCrossAttentionLayer(embed_dim, num_heads, dropout)
            for _ in range(num_layers)
        ])

        # Final layer norm
        self.final_norm = nn.LayerNorm(embed_dim)

        # Coordinate prediction head
        self.coord_prediction_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, 3)  # Predict 3D coordinates
        )

    def forward(self, mol_coords, solvent_embeddings):
        """
        Args:
            mol_coords: [batch_size, seq_len, 3] - molecular coordinates
            solvent_embeddings: [batch_size, embed_dim] - solvent property embeddings
        Returns:
            predicted_coords: [batch_size, seq_len, 3] - solvent-aware coordinates
        """
        batch_size, seq_len, _ = mol_coords.shape

        mol_coords = mol_coords.to(self.coord_proj.weight.dtype)
        coord_embeddings = self.coord_proj(mol_coords)  # [batch_size, seq_len, embed_dim]

        solvent_embeddings = solvent_embeddings.to(coord_embeddings.dtype)

        # Expand solvent embeddings to match sequence length
        # From [batch_size, embed_dim] to [batch_size, seq_len, embed_dim]
        solvent_queries = solvent_embeddings.unsqueeze(1).expand(batch_size, seq_len, -1)

        enhanced_representations = coord_embeddings
        for layer in self.layers:
            enhanced_representations = layer(enhanced_representations, solvent_queries)

        enhanced_representations = self.final_norm(enhanced_representations)

        # Predict coordinate deltas
        coord_deltas = self.coord_prediction_head(enhanced_representations)

        # Add deltas to original coordinates (residual connection)
        predicted_coords = mol_coords + coord_deltas

        return predicted_coords


@register_model("unimol")
class UniMolModel(BaseUnicoreModel):
    """
    UniMol Transformer Model for Molecular Representation Learning

    This model implements a transformer architecture specifically designed for
    molecular representation learning and protein-ligand binding prediction.
    It supports both standard molecular pretraining and complex protein-ligand
    pretraining modes.

    Key Components:
    - Transformer encoder with pair representations
    - Gaussian distance features for molecular graphs
    - Masked language modeling heads
    - Distance and coordinate prediction capabilities
    - Complex protein-ligand interaction modeling

    Attributes:
        args: Model configuration arguments
        padding_idx: Padding token index
        embed_tokens: Token embedding layer
        encoder: Main transformer encoder
        lig_encoder: Ligand-specific encoder (if enabled)
        classification_heads: Task-specific classification heads
    """

    @staticmethod
    def add_args(parser):
        """
        Add model-specific command line arguments.

        Args:
            parser: ArgumentParser instance to add arguments to
        """
        # Core architecture parameters
        parser.add_argument(
            "--encoder-layers",
            type=int,
            metavar="L",
            help="Number of encoder layers"
        )
        parser.add_argument(
            "--encoder-embed-dim",
            type=int,
            metavar="H",
            help="Encoder embedding dimension",
        )
        parser.add_argument(
            "--encoder-ffn-embed-dim",
            type=int,
            metavar="F",
            help="Encoder FFN embedding dimension",
        )
        parser.add_argument(
            "--encoder-attention-heads",
            type=int,
            metavar="A",
            help="Number of encoder attention heads",
        )

        # Activation functions
        parser.add_argument(
            "--activation-fn",
            choices=utils.get_available_activation_fns(),
            help="Activation function to use",
        )
        parser.add_argument(
            "--pooler-activation-fn",
            choices=utils.get_available_activation_fns(),
            help="Activation function for pooler layer",
        )

        # Dropout parameters
        parser.add_argument(
            "--emb-dropout",
            type=float,
            metavar="D",
            help="Dropout probability for embeddings",
        )
        parser.add_argument(
            "--dropout",
            type=float,
            metavar="D",
            help="General dropout probability"
        )
        parser.add_argument(
            "--attention-dropout",
            type=float,
            metavar="D",
            help="Dropout probability for attention weights",
        )
        parser.add_argument(
            "--activation-dropout",
            type=float,
            metavar="D",
            help="Dropout probability after activation in FFN",
        )
        parser.add_argument(
            "--pooler-dropout",
            type=float,
            metavar="D",
            help="Dropout probability in masked LM pooler layers",
        )

        # Sequence and architecture parameters
        parser.add_argument(
            "--max-seq-len",
            type=int,
            help="Maximum sequence length for positional embeddings"
        )
        parser.add_argument(
            "--post-ln",
            type=bool,
            help="Use post layer normalization instead of pre layer normalization"
        )

        # Loss function weights
        parser.add_argument(
            "--masked-token-loss",
            type=float,
            metavar="D",
            help="Weight for masked token loss",
        )
        parser.add_argument(
            "--masked-dist-loss",
            type=float,
            metavar="D",
            help="Weight for masked distance loss",
        )
        parser.add_argument(
            "--masked-coord-loss",
            type=float,
            metavar="D",
            help="Weight for masked coordinate loss",
        )
        parser.add_argument(
            "--x-norm-loss",
            type=float,
            metavar="D",
            help="Weight for x norm loss",
        )
        parser.add_argument(
            "--delta-pair-repr-norm-loss",
            type=float,
            metavar="D",
            help="Weight for delta encoder pair representation norm loss",
        )
        parser.add_argument(
            "--masked-coord-dist-loss",
            type=float,
            metavar="D",
            help="Weight for masked coordinate distance loss",
        )

        # Model mode
        parser.add_argument(
            "--mode",
            type=str,
            default="train",
            choices=["train", "infer"],
            help="Model operation mode",
        )

        # Complex pretraining configuration
        parser.add_argument(
            "--complex-pretrain",
            type=int,
            default=1,
            help="Enable complex protein-ligand pretraining (1: activate, 0: deactivate)",
        )
        parser.add_argument(
            "--idx-path",
            type=str,
            default=None,
            help="Path to index file for complex pretraining",
        )
        parser.add_argument(
            "--max-comnum",
            type=int,
            default=400,
            help="Maximum number of atoms in protein-ligand complex",
        )
        parser.add_argument(
            "--complex-layernum",
            type=int,
            default=3,
            help="Number of layers for complex interaction modeling",
        )
        parser.add_argument(
            "--dis-clsnum",
            type=int,
            default=61,
            help="Number of distance classes for classification",
        )

        # Ligand feature extraction
        parser.add_argument(
            "--online-ligfeat",
            type=int,
            default=0,
            help="Use online ligand feature extraction (1: enable, 0: disable)",
        )

        # Pretrained model paths
        parser.add_argument(
            "--lig-pretrained",
            type=str,
            default="",
            help="Path to pretrained ligand model",
        )
        parser.add_argument(
            "--proc-pretrained",
            type=str,
            default="",
            help="Path to pretrained protein model",
        )
        parser.add_argument(
            "--proc-freeze",
            type=int,
            default=1,
            help="Freeze protein encoder parameters (1: freeze, 0: trainable)",
        )

        # Cross-attention network configuration
        parser.add_argument(
            "--complex-crnet",
            type=int,
            default=0,
            help="Use cross-attention network like UniMol (1: enable, 0: disable)",
        )
        parser.add_argument(
            "--cr-regression",
            type=int,
            default=0,
            help="Use regression for distance prediction (1: enable, 0: disable)",
        )
        parser.add_argument(
            "--dist-threshold",
            type=float,
            default=8.0,
            help="Distance threshold between molecule and pocket for regression",
        )

        # Conformer generation
        parser.add_argument(
            "--rdkit-random",
            type=int,
            default=0,
            help="Use random seed for RDKit conformer generation (1: enable, 0: disable)",
        )

        # Contrastive learning parameters
        parser.add_argument(
            "--contrastive-temperature",
            type=float,
            default=0.07,
            help="Temperature parameter for contrastive learning (default: 0.07)",
        )
        parser.add_argument(
            "--contrastive-weight",
            type=float,
            default=1.0,
            help="Weight for contrastive learning loss (default: 1.0)",
        )

        # Feature masking configuration
        parser.add_argument(
            "--mask-ratio",
            type=float,
            default=0.8,
            help="Ratio of features to mask during training",
        )
        parser.add_argument(
            "--mask-feature",
            type=int,
            default=0,
            help="Enable feature masking (1: enable, 0: disable)",
        )

        # Model recycling
        parser.add_argument(
            "--recycling",
            type=int,
            default=3,
            help="Number of recycling iterations for decoder",
        )

    def __init__(self, args, dictionary, lig_dictionary: Optional[Any] = None):
        """
        Initialize UniMol model with configuration and dictionaries.

        Args:
            args: Model configuration arguments
            dictionary: Protein atom type dictionary
            lig_dictionary: Ligand atom type dictionary (optional)
        """
        super().__init__()
        base_architecture(args)
        self.args = args
        self.padding_idx = dictionary.pad()
        self._num_updates = None

        # Initialize core components
        self._init_embeddings(args, dictionary)
        self._init_encoder(args)
        self._init_gaussian_features(args, dictionary)

        # Initialize ligand-specific components if enabled
        if getattr(args, 'online_ligfeat', False) and lig_dictionary is not None:
            self._init_ligand_components(args, lig_dictionary)

        # Initialize task-specific heads
        self._init_task_heads(args, dictionary)

        # Initialize complex pretraining components
        if getattr(args, 'complex_pretrain', False):
            self._init_complex_components(args)

        # Initialize classification heads registry
        self.classification_heads = nn.ModuleDict()

        # self.solvent_property_encoder = SolventPropertyEncoder(embed_dim=args.mol.encoder_embed_dim)

        # self.solvent_cross_attention_transformer = SolventCrossAttentionTransformer(
        #     embed_dim=args.mol.encoder_embed_dim,
        #     num_heads=args.mol.encoder_attention_heads,
        #     num_layers=4,
        #     dropout=0.1
        # )

        # Apply parameter initialization
        self.apply(init_bert_params)

        # Load pretrained models if specified
        self._load_pretrained_models(args)

    def _init_embeddings(self, args, dictionary):
        """Initialize token embeddings."""
        self.embed_tokens = nn.Embedding(
            len(dictionary), args.encoder_embed_dim, self.padding_idx
        )

    def _init_encoder(self, args):
        """Initialize the main transformer encoder."""
        self.encoder = TransformerEncoderWithPair(
            encoder_layers=args.encoder_layers,
            embed_dim=args.encoder_embed_dim,
            ffn_embed_dim=args.encoder_ffn_embed_dim,
            attention_heads=args.encoder_attention_heads,
            emb_dropout=args.emb_dropout,
            dropout=args.dropout,
            attention_dropout=args.attention_dropout,
            activation_dropout=args.activation_dropout,
            max_seq_len=args.max_seq_len,
            activation_fn=args.activation_fn,
            no_final_head_layer_norm=args.delta_pair_repr_norm_loss < 0,
        )

    def _init_gaussian_features(self, args, dictionary):
        """Initialize Gaussian distance features."""
        K = DEFAULT_GAUSSIAN_K
        n_edge_type = len(dictionary) * len(dictionary)

        self.gbf_proj = NonLinearHead(
            K, args.encoder_attention_heads, args.activation_fn
        )
        self.gbf = GaussianLayer(K, n_edge_type)
    def _init_ligand_components(self, args, lig_dictionary):
        """Initialize ligand-specific encoder components."""
        K = DEFAULT_GAUSSIAN_K

        # Ligand token embeddings
        self.lig_embed_tokens = nn.Embedding(
            len(lig_dictionary), args.encoder_embed_dim, self.padding_idx
        )

        # Ligand transformer encoder
        self.lig_encoder = TransformerEncoderWithPair(
            encoder_layers=args.encoder_layers,
            embed_dim=args.encoder_embed_dim,
            ffn_embed_dim=args.encoder_ffn_embed_dim,
            attention_heads=args.encoder_attention_heads,
            emb_dropout=args.emb_dropout,
            dropout=args.dropout,
            attention_dropout=args.attention_dropout,
            activation_dropout=args.activation_dropout,
            max_seq_len=args.max_seq_len,
            activation_fn=args.activation_fn,
            no_final_head_layer_norm=args.delta_pair_repr_norm_loss < 0,
        )

        # Ligand Gaussian features
        self.lig_gbf_proj = NonLinearHead(
            K, args.encoder_attention_heads, args.activation_fn
        )
        lig_n_edge_type = len(lig_dictionary) * len(lig_dictionary)
        self.lig_gbf = GaussianLayer(K, lig_n_edge_type)

    def _init_task_heads(self, args, dictionary):
        """Initialize task-specific prediction heads."""
        # Masked language modeling head
        if args.masked_token_loss > 0:
            self.lm_head = MaskLMHead(
                embed_dim=args.encoder_embed_dim,
                output_dim=len(dictionary),
                activation_fn=args.activation_fn,
                weight=None,
            )

        # Coordinate prediction head
        if args.masked_coord_loss > 0:
            self.pair2coord_proj = NonLinearHead(
                args.encoder_attention_heads, 1, args.activation_fn
            )

        # Distance prediction head
        if args.masked_dist_loss > 0:
            self.dist_head = DistanceHead(
                args.encoder_attention_heads, args.activation_fn
            )


    def _init_complex_components(self, args):
        """Initialize components for complex protein-ligand pretraining."""
        # Feature masking components
        if getattr(args, 'mask_feature', False):
            self.mask_token_embedding = nn.Embedding(1, args.encoder_embed_dim)
            self.reconstruct_mask_feat_head = NonLinearHead(
                args.encoder_embed_dim, args.encoder_embed_dim, "relu"
            )

        # Cross-attention network components
        if getattr(args, 'complex_crnet', False):
            self.concat_decoder = TransformerEncoderWithPair(
                encoder_layers=4,
                embed_dim=args.encoder_embed_dim,
                ffn_embed_dim=args.encoder_ffn_embed_dim,
                attention_heads=args.encoder_attention_heads,
                emb_dropout=0.1,
                dropout=0.1,
                attention_dropout=0.1,
                activation_dropout=0.0,
                activation_fn="gelu",
            )

            # Distance prediction heads for cross-attention
            if getattr(args, 'cr_regression', False):
                input_dim = args.encoder_embed_dim * 2 + args.encoder_attention_heads
                self.cross_distance_project = NonLinearHead(
                    input_dim, 1, "relu"
                )
                self.holo_distance_project = DistanceHead(
                    args.encoder_embed_dim + args.encoder_attention_heads, "relu"
                )
            else:
                self.cls_dis_head = NonLinearHead(
                    args.encoder_embed_dim * 2 + args.encoder_attention_heads,
                    args.dis_clsnum, "relu"
                )
        else:
            # Standard complex layers
            self.complex_layernum = getattr(args, 'complex_layernum', 3)
            self.complex_layers = nn.ModuleList([
                TransformerEncoderLayer(
                    embed_dim=args.encoder_embed_dim,
                    ffn_embed_dim=args.encoder_ffn_embed_dim,
                    attention_heads=args.encoder_attention_heads,
                    dropout=args.emb_dropout,
                    attention_dropout=args.dropout,
                    activation_dropout=args.attention_dropout,
                    activation_fn=args.activation_fn,
                    post_ln=False,
                )
                for _ in range(self.complex_layernum)
            ])
            self.cls_dis_head = NonLinearHead(
                args.encoder_embed_dim * 2, args.dis_clsnum, args.activation_fn
            )

    def _load_pretrained_models(self, args):
        """Load pretrained models if specified."""
        if getattr(args, 'online_ligfeat', False) and hasattr(args, 'lig_pretrained'):
            if args.lig_pretrained:
                self.load_pretrained_model(args.lig_pretrained)

        if getattr(args, 'complex_pretrain', False) and hasattr(args, 'proc_pretrained'):
            if args.proc_pretrained:
                self.load_pocket_pretrained_model(args.proc_pretrained)

    @classmethod
    def build_model(cls, args, task):
        """
        Build a new UniMol model instance.

        Args:
            args: Model configuration arguments
            task: Task instance containing dictionaries

        Returns:
            UniMolModel: Initialized model instance
        """
        if getattr(args, 'complex_pretrain', False):
            return cls(args, task.dictionary, task.lig_dictionary)
        return cls(args, task.dictionary)

    def load_pocket_pretrained_model(self, poc_pretrained: str):
        """
        Load pretrained weights for the protein pocket encoder.

        Args:
            poc_pretrained: Path to pretrained protein model checkpoint
        """
        if not poc_pretrained:
            return

        logger.info(f"Loading pocket pretrained weights from {poc_pretrained}")

        try:
            poc_state_dict = torch.load(
                poc_pretrained,
                map_location=lambda storage, _: storage
            )
            missing_keys, not_matched_keys = self.load_state_dict(
                poc_state_dict['model'], strict=False
            )

            # Filter out ligand-related missing keys for cleaner logging
            filter_lig_keys = [k for k in missing_keys if not k.startswith('lig_')]

            logger.info(f"Loaded pocket model weights")
            logger.info(f"Missing keys (excluding ligand): {filter_lig_keys}")
            logger.info(f"Not matched keys: {not_matched_keys}")

            # Freeze protein encoder parameters if specified
            if getattr(self.args, 'proc_freeze', False):
                self._freeze_protein_components()

        except Exception as e:
            logger.error(f"Failed to load pocket pretrained model: {e}")
            raise

    def _freeze_protein_components(self):
        """Freeze protein encoder components."""
        components_to_freeze = [
            self.embed_tokens,
            self.gbf_proj,
            self.gbf,
            self.encoder
        ]

        for component in components_to_freeze:
            if hasattr(self, component.__class__.__name__.lower().replace('embedding', 'embed_tokens')):
                self.freeze_params(component)

    def load_pretrained_model(self, lig_pretrained: str):
        """
        Load pretrained weights for the ligand encoder.

        Args:
            lig_pretrained: Path to pretrained ligand model checkpoint
        """
        if not lig_pretrained:
            return

        logger.info(f"Loading pretrained ligand weights from {lig_pretrained}")

        try:
            state_dict = torch.load(
                lig_pretrained,
                map_location=lambda storage, _: storage
            )

            # Load token embeddings
            token_weight_dict = {
                'weight': state_dict['model']['embed_tokens.weight']
            }
            self.lig_embed_tokens.load_state_dict(token_weight_dict, strict=True)

            # Load GBF projection weights
            gbf_proj_weight_dict = {
                'linear1.weight': state_dict['model']['gbf_proj.linear1.weight'],
                'linear1.bias': state_dict['model']['gbf_proj.linear1.bias'],
                'linear2.weight': state_dict['model']['gbf_proj.linear2.weight'],
                'linear2.bias': state_dict['model']['gbf_proj.linear2.bias']
            }
            self.lig_gbf_proj.load_state_dict(gbf_proj_weight_dict, strict=True)

            # Load GBF weights with dimension checking
            pretrained_mul_weight = state_dict['model']['gbf.mul.weight']
            pretrained_bias_weight = state_dict['model']['gbf.bias.weight']

            # Check if dimensions match current model
            current_edge_types = self.lig_gbf.mul.num_embeddings
            pretrained_edge_types = pretrained_mul_weight.size(0)

            if current_edge_types != pretrained_edge_types:
                logger.warning(
                    f"Edge type dimension mismatch: current model expects {current_edge_types}, "
                    f"but pretrained model has {pretrained_edge_types}. "
                    f"Resizing embeddings to match pretrained weights."
                )

                # Resize the embeddings to match pretrained weights
                self.lig_gbf.mul = nn.Embedding(pretrained_edge_types, 1)
                self.lig_gbf.bias = nn.Embedding(pretrained_edge_types, 1)

                # Re-initialize the resized embeddings
                nn.init.constant_(self.lig_gbf.bias.weight, 0)
                nn.init.constant_(self.lig_gbf.mul.weight, 1)

            gbf_weight_dict = {
                'means.weight': state_dict['model']['gbf.means.weight'],
                'stds.weight': state_dict['model']['gbf.stds.weight'],
                'mul.weight': pretrained_mul_weight,
                'bias.weight': pretrained_bias_weight
            }
            self.lig_gbf.load_state_dict(gbf_weight_dict, strict=True)

            # Load encoder weights
            model_dict = {
                k.replace('encoder.', ''): v
                for k, v in state_dict['model'].items()
            }
            missing_keys, not_matched_keys = self.lig_encoder.load_state_dict(
                model_dict, strict=False
            )

            logger.info(f"Loaded ligand model weights")
            logger.info(f"Missing keys: {missing_keys}")
            logger.info(f"Not matched keys: {not_matched_keys}")

            # Freeze ligand encoder parameters
            self._freeze_ligand_components()

        except Exception as e:
            logger.error(f"Failed to load ligand pretrained model: {e}")
            raise

    def _freeze_ligand_components(self):
        """Freeze ligand encoder components."""
        components_to_freeze = [
            self.lig_embed_tokens,
            self.lig_gbf_proj,
            self.lig_gbf,
            self.lig_encoder
        ]

        for component in components_to_freeze:
            if hasattr(self, component.__class__.__name__.lower()):
                self.freeze_params(component)

    def freeze_params(self, model: nn.Module):
        """
        Freeze parameters of a given model.

        Args:
            model: PyTorch module to freeze
        """
        for param in model.parameters():
            param.requires_grad = False

    def check_lig_eval(self):
        """Ensure ligand encoder components are in evaluation mode."""
        ligand_components = [
            self.lig_encoder,
            self.lig_embed_tokens,
            self.lig_gbf_proj,
            self.lig_gbf
        ]

        for component in ligand_components:
            if hasattr(self, component.__class__.__name__.lower()) and component.training:
                component.eval()

    def check_pocket_eval(self):
        """Ensure protein encoder components are in evaluation mode."""
        protein_components = [
            self.embed_tokens,
            self.gbf_proj,
            self.gbf,
            self.encoder
        ]

        for component in protein_components:
            if component.training:
                component.eval()


    def crnet_forward(self, encoder_rep, encoder_pair_rep, prot_padding_mask, lig_encoder_rep, lig_graph_attn_bias, lig_padding_mask, lig_encoder_pair_rep):
        assert self.args.online_ligfeat == 1
        pocket_encoder_rep = encoder_rep
        pocket_encoder_pair_rep = encoder_pair_rep
        pocket_padding_mask = prot_padding_mask

        mol_encoder_rep = lig_encoder_rep
        mol_graph_attn_bias = lig_graph_attn_bias
        mol_padding_mask = lig_padding_mask
        mol_encoder_pair_rep = lig_encoder_pair_rep

        mol_sz = lig_encoder_rep.size(1)
        pocket_sz = pocket_encoder_rep.size(1)

        concat_rep = torch.cat(
            [mol_encoder_rep, pocket_encoder_rep], dim=-2
        )  # [batch, mol_sz+pocket_sz, hidden_dim]
        concat_mask = torch.cat(
            [mol_padding_mask, pocket_padding_mask], dim=-1
        )  # [batch, mol_sz+pocket_sz]
        attn_bs = mol_graph_attn_bias.size(0)

        concat_attn_bias = torch.zeros(
            attn_bs, mol_sz + pocket_sz, mol_sz + pocket_sz
        ).type_as(
            concat_rep
        )  # [batch, mol_sz+pocket_sz, mol_sz+pocket_sz]
        concat_attn_bias[:, :mol_sz, :mol_sz] = (
            mol_encoder_pair_rep.permute(0, 3, 1, 2)
            .reshape(-1, mol_sz, mol_sz)
            .contiguous()
        )
        concat_attn_bias[:, -pocket_sz:, -pocket_sz:] = (
            pocket_encoder_pair_rep.permute(0, 3, 1, 2)
            .reshape(-1, pocket_sz, pocket_sz)
            .contiguous()
        )

        decoder_rep = concat_rep
        decoder_pair_rep = concat_attn_bias
        for i in range(self.args.recycling):
            decoder_outputs = self.concat_decoder(
                decoder_rep, padding_mask=concat_mask, attn_mask=decoder_pair_rep
            )
            decoder_rep = decoder_outputs[0]
            decoder_pair_rep = decoder_outputs[1]
            if i != (self.args.recycling - 1):
                decoder_pair_rep = decoder_pair_rep.permute(0, 3, 1, 2).reshape(
                    -1, mol_sz + pocket_sz, mol_sz + pocket_sz
                )

        mol_decoder = decoder_rep[:, :mol_sz]
        pocket_decoder = decoder_rep[:, mol_sz:]

        return mol_decoder, pocket_decoder, decoder_pair_rep

    def forward(
        self,
        src_tokens: torch.Tensor,
        src_distance: torch.Tensor,
        src_coord: torch.Tensor,
        src_edge_type: torch.Tensor,
        encoder_masked_tokens: Optional[torch.Tensor] = None,
        features_only: bool = False,
        classification_head_name: Optional[str] = None,
        lig_feat_input: Optional[torch.Tensor] = None,
        lig_num_lst: Optional[torch.Tensor] = None,
        prot_num_lst: Optional[torch.Tensor] = None,
        lig_tokens: Optional[torch.Tensor] = None,
        lig_distance: Optional[torch.Tensor] = None,
        lig_coord: Optional[torch.Tensor] = None,
        lig_edge_type: Optional[torch.Tensor] = None,
        lig_org_distance: Optional[torch.Tensor] = None,
        solvent_lig_distance: Optional[torch.Tensor] = None,
        solvent_lig_coord: Optional[torch.Tensor] = None,
        solvent_lig_org_distance: Optional[torch.Tensor] = None,
        solvent_properties: List = None,
        feat_masking_idx: Optional[torch.Tensor] = None,
        **kwargs
    ) -> Union[Tuple[torch.Tensor, ...], torch.Tensor]:

        if classification_head_name is not None:
            features_only = True

        # Optimize padding mask computation
        padding_mask = src_tokens.eq(self.padding_idx)
        if self.args.complex_pretrain:
            self.check_lig_eval()
            if self.args.proc_freeze:
                self.check_pocket_eval()
            prot_padding_mask = padding_mask

        # Early exit if no valid tokens
        if not padding_mask.any():
            padding_mask = None

        # Optimized distance feature computation function
        def compute_gbf_features(gbf_layer, gbf_proj_layer, dist, et):
            """Optimized distance feature computation"""
            n_node = dist.size(-1)
            gbf_feature = gbf_layer(dist, et)
            gbf_result = gbf_proj_layer(gbf_feature)
            # Use contiguous view for better memory layout
            return gbf_result.permute(0, 3, 1, 2).contiguous().view(-1, n_node, n_node)

        # Pre-compute distance features to avoid redundant calculations
        graph_attn_bias = compute_gbf_features(self.gbf, self.gbf_proj, src_distance, src_edge_type)

        # Compute embeddings and encoder outputs
        x = self.embed_tokens(src_tokens)

        if self.args.complex_pretrain and self.args.proc_freeze:
            with torch.no_grad():
                (
                    encoder_rep,
                    encoder_pair_rep,
                    delta_encoder_pair_rep,
                    x_norm,
                    delta_encoder_pair_rep_norm,
                ) = self.encoder(x, padding_mask=padding_mask, attn_mask=graph_attn_bias)
        else:
            (
                encoder_rep,
                encoder_pair_rep,
                delta_encoder_pair_rep,
                x_norm,
                delta_encoder_pair_rep_norm,
            ) = self.encoder(x, padding_mask=padding_mask, attn_mask=graph_attn_bias)


        encoder_pair_rep[encoder_pair_rep == float("-inf")] = 0

        encoder_distance = None
        encoder_coord = None
        solvent_lig_coord_target = None
        # solvent_lig_coord_predict = None

        if hasattr(self.args, 'complex_pretrain') and self.args.complex_pretrain:
            if hasattr(self.args, 'online_ligfeat') and self.args.online_ligfeat:
                # Pre-compute ligand distance features to avoid redundant calculations
                lig_graph_attn_bias = compute_gbf_features(self.lig_gbf, self.lig_gbf_proj, lig_distance, lig_edge_type)
                solvent_lig_graph_attn_bias = compute_gbf_features(self.lig_gbf, self.lig_gbf_proj, solvent_lig_distance, lig_edge_type)

                with torch.no_grad():
                    lig_x = self.lig_embed_tokens(lig_tokens)
                    lig_padding_mask = lig_tokens.eq(self.padding_idx)

                    # Compute ligand encoder outputs
                    (
                        lig_encoder_rep,
                        lig_encoder_pair_rep,
                        lig_delta_encoder_pair_rep,
                        lig_x_norm,
                        lig_delta_encoder_pair_rep_norm,
                    ) = self.lig_encoder(lig_x, padding_mask=lig_padding_mask, attn_mask=lig_graph_attn_bias)

                    # Compute solvent-aware ligand encoder outputs
                    (
                        solvent_lig_encoder_rep,
                        solvent_lig_encoder_pair_rep,
                        solvent_lig_delta_encoder_pair_rep,
                        solvent_lig_x_norm,
                        solvent_lig_delta_encoder_pair_rep_norm,
                    ) = self.lig_encoder(lig_x, padding_mask=lig_padding_mask, attn_mask=solvent_lig_graph_attn_bias)

                    # Handle feature masking if needed
                    if lig_org_distance is not None:
                        lig_graph_attn_bias_org = compute_gbf_features(self.lig_gbf, self.lig_gbf_proj, lig_org_distance, lig_edge_type)

                        (
                            lig_encoder_rep_org,
                            lig_encoder_pair_rep_org,
                            _,
                            _,
                            _,
                        ) = self.lig_encoder(lig_x, padding_mask=lig_padding_mask, attn_mask=lig_graph_attn_bias_org)

                        # Optimize masking operations - avoid unnecessary cloning
                        if feat_masking_idx is not None:
                            feat_masking_idx = feat_masking_idx.to(torch.bool)
                            lig_encoder_rep_unmask = lig_encoder_rep.clone()
                            mask_token = self.mask_token_embedding(torch.tensor(0, device=lig_encoder_rep.device))
                            lig_encoder_rep[feat_masking_idx] = mask_token
                            lig_feature_reg_target = lig_encoder_rep_org[feat_masking_idx]

                            if solvent_lig_org_distance is not None:
                                solvent_lig_graph_attn_bias_org = compute_gbf_features(self.lig_gbf, self.lig_gbf_proj, solvent_lig_org_distance, lig_edge_type)

                                (
                                    solvent_lig_encoder_rep_org,
                                    solvent_lig_encoder_pair_rep_org,
                                    _,
                                    _,
                                    _,
                                ) = self.lig_encoder(lig_x, padding_mask=lig_padding_mask, attn_mask=solvent_lig_graph_attn_bias_org)

                                solvent_lig_encoder_rep_unmask = solvent_lig_encoder_rep.clone()
                                solvent_lig_encoder_rep[feat_masking_idx] = mask_token
                                solvent_lig_feature_reg_target = solvent_lig_encoder_rep_org[feat_masking_idx]


                # Optimize concatenation operations - use pre-allocated tensors when possible
                all_padding_mask = torch.cat([prot_padding_mask, lig_padding_mask], dim=1)
                # NOTE cls and sep for the ligand
                all_feat_concat = torch.cat([encoder_rep, lig_encoder_rep], dim=1)
            else:
                # Ensure dtype consistency without unnecessary conversions
                lig_feat_input_typed = lig_feat_input.to(encoder_rep.dtype) if lig_feat_input.dtype != encoder_rep.dtype else lig_feat_input
                all_feat_concat = torch.cat([encoder_rep, lig_feat_input_typed], dim=1)

            all_feat_x = all_feat_concat

            # Create solvent-aware complex features for contrastive learning
            if self.args.online_ligfeat and hasattr(self, 'lig_encoder'):
                # Create solvent-aware complex features by concatenating protein and solvent-aware ligand
                all_feat_x_solvent = torch.cat([encoder_rep, solvent_lig_encoder_rep], dim=1)
            else:
                # If no solvent features available, use the same as regular complex
                all_feat_x_solvent = all_feat_x

            if self.args.complex_crnet:
                # all_padding_mask = torch.cat([lig_padding_mask, prot_padding_mask], dim=1)
                concat_mask = torch.cat(
                        [lig_padding_mask, prot_padding_mask], dim=-1
                    )
                if self.args.mask_feature:
                    mask_mol_decoder, _, _ = self.crnet_forward(encoder_rep, encoder_pair_rep, prot_padding_mask, lig_encoder_rep, lig_graph_attn_bias, lig_padding_mask, lig_encoder_pair_rep)
                    mask_solvent_lig_decoder, _, _ = self.crnet_forward(encoder_rep, encoder_pair_rep, prot_padding_mask, solvent_lig_encoder_rep, solvent_lig_graph_attn_bias, lig_padding_mask, solvent_lig_encoder_pair_rep)
                    reconstruct_feat = self.reconstruct_mask_feat_head(mask_mol_decoder[feat_masking_idx])
                    reconstruct_solvent_lig_feat = self.reconstruct_mask_feat_head(mask_solvent_lig_decoder[feat_masking_idx])

                    mol_decoder, pocket_decoder, decoder_pair_rep = self.crnet_forward(encoder_rep, encoder_pair_rep, prot_padding_mask, lig_encoder_rep_unmask, lig_graph_attn_bias, lig_padding_mask, lig_encoder_pair_rep)
                    # Add solvent decoder computation for unmasked features
                    solvent_mol_decoder, solvent_pocket_decoder, solvent_decoder_pair_rep = self.crnet_forward(encoder_rep, encoder_pair_rep, prot_padding_mask, solvent_lig_encoder_rep_unmask, solvent_lig_graph_attn_bias, lig_padding_mask, solvent_lig_encoder_pair_rep)
                else:
                    mol_decoder, pocket_decoder, decoder_pair_rep = self.crnet_forward(encoder_rep, encoder_pair_rep, prot_padding_mask, lig_encoder_rep, lig_graph_attn_bias, lig_padding_mask, lig_encoder_pair_rep)
                    # Add solvent decoder computation for regular features
                    solvent_mol_decoder, solvent_pocket_decoder, solvent_decoder_pair_rep = self.crnet_forward(encoder_rep, encoder_pair_rep, prot_padding_mask, solvent_lig_encoder_rep, solvent_lig_graph_attn_bias, lig_padding_mask, solvent_lig_encoder_pair_rep)

                mol_sz = mol_decoder.size(1)
                pocket_sz = pocket_decoder.size(1)

                mol_pair_decoder_rep = decoder_pair_rep[:, :mol_sz, :mol_sz, :]
                mol_pocket_pair_decoder_rep = (
                    decoder_pair_rep[:, :mol_sz, mol_sz:, :]
                    + decoder_pair_rep[:, mol_sz:, :mol_sz, :].transpose(1, 2)
                ) * 0.5
                mol_pocket_pair_decoder_rep[mol_pocket_pair_decoder_rep == float("-inf")] = 0

                # Extract solvent-related pair representations
                solvent_mol_pocket_pair_decoder_rep = (
                    solvent_decoder_pair_rep[:, :mol_sz, mol_sz:, :]
                    + solvent_decoder_pair_rep[:, mol_sz:, :mol_sz, :].transpose(1, 2)
                ) * 0.5
                solvent_mol_pocket_pair_decoder_rep[solvent_mol_pocket_pair_decoder_rep == float("-inf")] = 0

                cross_rep = torch.cat(
                    [
                        mol_pocket_pair_decoder_rep,
                        mol_decoder.unsqueeze(-2).repeat(1, 1, pocket_sz, 1),
                        pocket_decoder.unsqueeze(-3).repeat(1, mol_sz, 1, 1),
                    ],
                    dim=-1,
                )  # [batch, mol_sz, pocket_sz, 4*hidden_size]

                solvent_cross_rep = torch.cat(
                    [
                        solvent_mol_pocket_pair_decoder_rep,
                        solvent_mol_decoder.unsqueeze(-2).repeat(1, 1, pocket_sz, 1),
                        solvent_pocket_decoder.unsqueeze(-3).repeat(1, mol_sz, 1, 1),
                    ],
                    dim=-1,
                )  # [batch, mol_sz, pocket_sz, 4*hidden_size]

                if self.args.cr_regression:
                    # directly regress the dist matrix
                    cross_distance_predict = (
                    F.elu(self.cross_distance_project(cross_rep).squeeze(-1)) + 1.0
                    )  # batch, mol_sz, pocket_sz
                    solvent_cross_distance_predict = (
                    F.elu(self.cross_distance_project(solvent_cross_rep).squeeze(-1)) + 1.0
                    )

                    dis_cls_logits = cross_distance_predict # regression target
                    # For regression mode, solvent distance logits are the same as regular
                    solvent_dis_cls_logits = solvent_cross_distance_predict
                else:
                    dis_cls_logits_matrix = self.cls_dis_head(cross_rep)
                    solvent_dis_cls_logits_matrix = self.cls_dis_head(solvent_cross_rep)

                    # Optimize logits collection using vectorized operations instead of loops
                    batch_size = dis_cls_logits_matrix.shape[0]

                    # Pre-allocate lists for better memory efficiency
                    logits_ele_lst = []
                    solvent_logits_ele_lst = []

                    # Vectorize the batch processing where possible
                    for s_idx in range(batch_size):
                        proc_num = prot_num_lst[s_idx].item()
                        lig_num = lig_num_lst[s_idx].item()

                        # Process regular logits with optimized slicing
                        logits_ele = dis_cls_logits_matrix[s_idx, 1:1+lig_num, 1:1+proc_num, :]
                        # Use view instead of reshape when possible for better performance
                        logits_ele = logits_ele.transpose(0, 1).contiguous().view(-1, logits_ele.shape[-1])
                        logits_ele_lst.append(logits_ele)

                        # Process solvent logits with optimized slicing
                        solvent_logits_ele = solvent_dis_cls_logits_matrix[s_idx, 1:1+lig_num, 1:1+proc_num, :]
                        solvent_logits_ele = solvent_logits_ele.transpose(0, 1).contiguous().view(-1, solvent_logits_ele.shape[-1])
                        solvent_logits_ele_lst.append(solvent_logits_ele)

                    # Use more efficient concatenation
                    dis_cls_logits = torch.cat(logits_ele_lst, dim=0)
                    solvent_dis_cls_logits = torch.cat(solvent_logits_ele_lst, dim=0)


            else:
                # Process both regular and solvent-aware features through complex layers
                for i in range(len(self.complex_layers)):
                    all_feat_x = self.complex_layers[i](
                        all_feat_x, padding_mask=all_padding_mask
                    )
                    all_feat_x_solvent = self.complex_layers[i](
                        all_feat_x_solvent, padding_mask=all_padding_mask
                    )

                # Optimize feature collection with reduced memory allocation
                batch_size = all_feat_x.shape[0]

                def extract_and_combine_features(feat_tensor, padding_mask, prot_nums, lig_nums, online_ligfeat):
                    """Optimized feature extraction and combination"""
                    proc_lig_feat_lst = []

                    for s_idx in range(batch_size):
                        filter_idx = ~padding_mask[s_idx]
                        proc_lig_feat = feat_tensor[s_idx][filter_idx]
                        proc_num = prot_nums[s_idx].item()
                        lig_num = lig_nums[s_idx].item()

                        # Extract features with optimized indexing
                        proc_feat = proc_lig_feat[1:1+proc_num, :]
                        if online_ligfeat:
                            lig_feat = proc_lig_feat[3+proc_num:3+proc_num+lig_num, :]
                        else:
                            lig_feat = proc_lig_feat[2+proc_num:, :]

                        # Use expand instead of repeat for memory efficiency
                        proc_feat_expanded = proc_feat.unsqueeze(1).expand(-1, lig_num, -1)
                        lig_feat_expanded = lig_feat.unsqueeze(0).expand(proc_num, -1, -1)

                        # Combine features efficiently
                        mix_concat_matrix = torch.cat([proc_feat_expanded, lig_feat_expanded], dim=2)
                        mix_concat_matrix_flat = mix_concat_matrix.view(-1, mix_concat_matrix.shape[-1])
                        proc_lig_feat_lst.append(mix_concat_matrix_flat)

                    return torch.cat(proc_lig_feat_lst, dim=0)

                # Extract features for regular complex
                proc_lig_feat_combined = extract_and_combine_features(
                    all_feat_x, all_padding_mask, prot_num_lst, lig_num_lst, self.args.online_ligfeat
                )
                dis_cls_logits = self.cls_dis_head(proc_lig_feat_combined)

                # Extract features for solvent-aware complex
                solvent_proc_lig_feat_combined = extract_and_combine_features(
                    all_feat_x_solvent, all_padding_mask, prot_num_lst, lig_num_lst, self.args.online_ligfeat
                )
                solvent_dis_cls_logits = self.cls_dis_head(solvent_proc_lig_feat_combined)

            if self.args.mask_feature:
                return all_feat_x, all_padding_mask, dis_cls_logits, solvent_dis_cls_logits, x_norm, delta_encoder_pair_rep_norm, (reconstruct_feat, lig_feature_reg_target), (reconstruct_solvent_lig_feat, solvent_lig_feature_reg_target)

            return all_feat_x, all_padding_mask, dis_cls_logits, solvent_dis_cls_logits, x_norm, delta_encoder_pair_rep_norm

        if not features_only:
            if self.args.masked_token_loss > 0:
                logits = self.lm_head(encoder_rep, encoder_masked_tokens)
            if self.args.masked_coord_loss > 0:
                coords_emb = src_coord
                if padding_mask is not None:
                    atom_num = (torch.sum(1 - padding_mask.type_as(x), dim=1) - 1).view(
                        -1, 1, 1, 1
                    )
                else:
                    atom_num = src_coord.shape[1] - 1
                delta_pos = coords_emb.unsqueeze(1) - coords_emb.unsqueeze(2)
                attn_probs = self.pair2coord_proj(delta_encoder_pair_rep)
                coord_update = delta_pos / atom_num * attn_probs
                coord_update = torch.sum(coord_update, dim=2)
                encoder_coord = coords_emb + coord_update

                # solvent_lig_coord_predict = self.solvent_cross_attention_transformer(
                #     mol_coords=lig_coord,
                #     solvent_embeddings=solvent_embeddings
                # )
            if self.args.masked_dist_loss > 0:
                encoder_distance = self.dist_head(encoder_pair_rep)

        if classification_head_name is not None:
            logits = self.classification_heads[classification_head_name](encoder_rep)
        if self.args.mode == 'infer':
            return encoder_rep, encoder_pair_rep
        else:
            return (
                logits,
                encoder_distance,
                encoder_coord,
                # solvent_lig_coord_target,
                # solvent_lig_coord_predict,
                x_norm,
                delta_encoder_pair_rep_norm,
            )

    def register_classification_head(
        self, name, num_classes=None, inner_dim=None, **kwargs
    ):
        """Register a classification head."""
        if name in self.classification_heads:
            prev_num_classes = self.classification_heads[name].out_proj.out_features
            prev_inner_dim = self.classification_heads[name].dense.out_features
            if num_classes != prev_num_classes or inner_dim != prev_inner_dim:
                logger.warning(
                    're-registering head "{}" with num_classes {} (prev: {}) '
                    "and inner_dim {} (prev: {})".format(
                        name, num_classes, prev_num_classes, inner_dim, prev_inner_dim
                    )
                )
        self.classification_heads[name] = ClassificationHead(
            input_dim=self.args.encoder_embed_dim,
            inner_dim=inner_dim or self.args.encoder_embed_dim,
            num_classes=num_classes,
            activation_fn=self.args.pooler_activation_fn,
            pooler_dropout=self.args.pooler_dropout,
        )

    def set_num_updates(self, num_updates):
        """State from trainer to pass along to model at every update."""
        self._num_updates = num_updates

    def get_num_updates(self):
        return self._num_updates


class MaskLMHead(nn.Module):
    """Head for masked language modeling."""

    def __init__(self, embed_dim, output_dim, activation_fn, weight=None):
        super().__init__()
        self.dense = nn.Linear(embed_dim, embed_dim)
        self.activation_fn = utils.get_activation_fn(activation_fn)
        self.layer_norm = LayerNorm(embed_dim)

        if weight is None:
            weight = nn.Linear(embed_dim, output_dim, bias=False).weight
        self.weight = weight
        self.bias = nn.Parameter(torch.zeros(output_dim))

    def forward(self, features, masked_tokens=None, **kwargs):
        # Only project the masked tokens while training,
        # saves both memory and computation
        if masked_tokens is not None:
            features = features[masked_tokens, :]

        x = self.dense(features)
        x = self.activation_fn(x)
        x = self.layer_norm(x)
        # project back to size of vocabulary with bias
        x = F.linear(x, self.weight) + self.bias
        return x


class ClassificationHead(nn.Module):
    """Head for sentence-level classification tasks."""

    def __init__(
        self,
        input_dim,
        inner_dim,
        num_classes,
        activation_fn,
        pooler_dropout,
    ):
        super().__init__()
        self.dense = nn.Linear(input_dim, inner_dim)
        self.activation_fn = utils.get_activation_fn(activation_fn)
        self.dropout = nn.Dropout(p=pooler_dropout)
        self.out_proj = nn.Linear(inner_dim, num_classes)

    def forward(self, features, **kwargs):
        x = features[:, 0, :]  # take <s> token (equiv. to [CLS])
        x = self.dropout(x)
        x = self.dense(x)
        x = self.activation_fn(x)
        x = self.dropout(x)
        x = self.out_proj(x)
        return x


class NonLinearHead(nn.Module):
    """Head for simple classification tasks."""

    def __init__(
        self,
        input_dim,
        out_dim,
        activation_fn,
        hidden=None,
    ):
        super().__init__()
        hidden = input_dim if not hidden else hidden
        self.linear1 = nn.Linear(input_dim, hidden)
        self.linear2 = nn.Linear(hidden, out_dim)
        self.activation_fn = utils.get_activation_fn(activation_fn)

    def forward(self, x):
        x = self.linear1(x)
        x = self.activation_fn(x)
        x = self.linear2(x)
        return x


class DistanceHead(nn.Module):
    def __init__(
        self,
        heads,
        activation_fn,
    ):
        super().__init__()
        self.dense = nn.Linear(heads, heads)
        self.layer_norm = nn.LayerNorm(heads)
        self.out_proj = nn.Linear(heads, 1)
        self.activation_fn = utils.get_activation_fn(activation_fn)

    def forward(self, x):
        bsz, seq_len, seq_len, _ = x.size()
        x[x == float('-inf')] = 0
        x = self.dense(x)
        x = self.activation_fn(x)
        x = self.layer_norm(x)
        x = self.out_proj(x).view(bsz, seq_len, seq_len)
        x = (x + x.transpose(-1, -2)) * 0.5
        return x


@torch.jit.script
def gaussian(x: torch.Tensor, mean: torch.Tensor, std: torch.Tensor) -> torch.Tensor:
    """
    Compute Gaussian function values.

    Args:
        x: Input tensor
        mean: Mean values
        std: Standard deviation values

    Returns:
        Gaussian function values
    """
    PI = 3.14159
    a = (2 * PI) ** 0.5
    return torch.exp(-0.5 * (((x - mean) / std) ** 2)) / (a * std)


class GaussianLayer(nn.Module):
    """
    Gaussian basis function layer for distance encoding.

    This layer encodes distances using Gaussian basis functions, which is
    essential for the model to understand spatial relationships in molecular
    structures.

    Args:
        K: Number of Gaussian basis functions
        edge_types: Number of edge types in the molecular graph
    """

    def __init__(self, K: int = DEFAULT_GAUSSIAN_K, edge_types: int = 1024):
        super().__init__()
        self.K = K

        # Gaussian parameters
        self.means = nn.Embedding(1, K)
        self.stds = nn.Embedding(1, K)

        # Edge-specific scaling parameters
        self.mul = nn.Embedding(edge_types, 1)
        self.bias = nn.Embedding(edge_types, 1)

        # Initialize parameters
        self._init_parameters()

    def _init_parameters(self):
        """Initialize Gaussian layer parameters."""
        nn.init.uniform_(self.means.weight, 0, 3)
        nn.init.uniform_(self.stds.weight, 0, 3)
        nn.init.constant_(self.bias.weight, 0)
        nn.init.constant_(self.mul.weight, 1)

    def forward(self, x, edge_type):
        mul = self.mul(edge_type).type_as(x)
        bias = self.bias(edge_type).type_as(x)
        x = mul * x.unsqueeze(-1) + bias
        x = x.expand(-1, -1, -1, self.K)
        mean = self.means.weight.float().view(-1)
        std = self.stds.weight.float().view(-1).abs() + 1e-5
        return gaussian(x.float(), mean, std).type_as(self.means.weight)


@register_model_architecture("unimol", "unimol")
def base_architecture(args):
    args.encoder_layers = getattr(args, "encoder_layers", 15)
    args.encoder_embed_dim = getattr(args, "encoder_embed_dim", 512)
    args.encoder_ffn_embed_dim = getattr(args, "encoder_ffn_embed_dim", 2048)
    args.encoder_attention_heads = getattr(args, "encoder_attention_heads", 64)
    args.dropout = getattr(args, "dropout", 0.1)
    args.emb_dropout = getattr(args, "emb_dropout", 0.1)
    args.attention_dropout = getattr(args, "attention_dropout", 0.1)
    args.activation_dropout = getattr(args, "activation_dropout", 0.0)
    args.pooler_dropout = getattr(args, "pooler_dropout", 0.0)
    args.max_seq_len = getattr(args, "max_seq_len", 512)
    args.activation_fn = getattr(args, "activation_fn", "gelu")
    args.pooler_activation_fn = getattr(args, "pooler_activation_fn", "tanh")
    args.post_ln = getattr(args, "post_ln", False)
    args.masked_token_loss = getattr(args, "masked_token_loss", -1.0)
    args.masked_coord_loss = getattr(args, "masked_coord_loss", -1.0)
    args.masked_dist_loss = getattr(args, "masked_dist_loss", -1.0)
    args.x_norm_loss = getattr(args, "x_norm_loss", -1.0)
    args.delta_pair_repr_norm_loss = getattr(args, "delta_pair_repr_norm_loss", -1.0)
    args.contrastive_temperature = getattr(args, "contrastive_temperature", 0.07)
    args.contrastive_weight = getattr(args, "contrastive_weight", 1.0)


@register_model_architecture("unimol", "unimol_base")
def unimol_base_architecture(args):
    base_architecture(args)