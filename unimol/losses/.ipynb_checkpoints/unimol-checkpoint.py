# Copyright (c) DP Technology.
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

"""
UniMol Loss Functions for Protein-Ligand Binding Research

This module implements comprehensive loss functions for the UniMol model,
supporting both standard molecular pretraining and complex protein-ligand
binding prediction tasks. The loss functions are designed for academic
research in computational biology and drug discovery.

Key Features:
- Masked language modeling loss for molecular representation learning
- Distance-based regression and classification losses
- Coordinate prediction losses for 3D molecular structures
- Feature masking losses for self-supervised learning
- Contrastive learning for protein-ligand binding prediction
- Multi-scale contrastive learning across structural hierarchies
- Comprehensive metrics logging and monitoring

Author: DP Technology
License: MIT
"""

import logging
import os
from typing import Dict, Any, List, Optional, Tuple, Union

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import distributions
from torch.distributions import Normal, kl_divergence

from unicore import metrics
from unicore.losses import UnicoreLoss, register_loss

# Configure logger for this module
logger = logging.getLogger(__name__)

# Constants for loss computation
DEFAULT_DIST_MEAN = 6.312581655060595
DEFAULT_DIST_STD = 3.3899264663911888
DISTANCE_INTERVAL_FINE = 0.2  # Fine-grained interval for distances 0-10
DISTANCE_INTERVAL_COARSE = 1.0  # Coarse interval for distances 10-20
MAX_DISTANCE_THRESHOLD = 1000.0


def _get_local_rank() -> int:
    try:
        return int(os.environ.get('LOCAL_RANK', '0'))
    except (ValueError, TypeError):
        return 0

def _init_wandb(project_name: str, run_name: str) -> bool:
    try:
        if _get_local_rank() == 0:
            import wandb
            api_key = os.environ.get('WANDB_API_KEY', '')
            if api_key:
                wandb.login(key=api_key)
                wandb.init(project=project_name, name=run_name)
                return True
            else:
                logger.warning("WANDB_API_KEY not found, skipping wandb initialization")
        return False
    except Exception as e:
        logger.warning(f"Failed to initialize wandb: {e}")
        return False


def _create_atom_similarity_distribution(vocab_size: int, std_dev: float = 1.5) -> torch.Tensor:
    pdf_matrix = torch.zeros(vocab_size, vocab_size)
    pdf_matrix[0, 0] = 1.0

    for atom_idx in range(1, vocab_size):
        atom_indices = torch.arange(1, vocab_size, dtype=torch.float)
        normal_dist = distributions.Normal(float(atom_idx), std_dev)

        probabilities = normal_dist.log_prob(atom_indices).exp()
        probabilities[atom_idx - 1] *= 2.0
        probabilities = probabilities / probabilities.sum()
        pdf_matrix[atom_idx, 1:] = probabilities

    return pdf_matrix


@register_loss("unimol")
class UniMolLoss(UnicoreLoss):
    """
    Comprehensive loss function for UniMol model training.
    """

    def __init__(self, task):
        super().__init__(task)
        self.padding_idx = task.dictionary.pad()
        self.seed = task.seed
        self.args = task.args

        # Distance normalization parameters
        self.dist_mean = DEFAULT_DIST_MEAN
        self.dist_std = DEFAULT_DIST_STD

        # Initialize wandb logging
        self.wandb_enabled = _init_wandb("Pretraining", task.args.run_name)

        # Configuration flags
        self.complex_pretrain = getattr(task.args, 'complex_pretrain', False)
        self.mask_feature = getattr(task.args, 'mask_feature', False)

        self.gce_std_dev = getattr(task.args, 'gce_std_dev', 1.5)


        # Initialize atom similarity distribution for GCE loss
        vocab_size = len(task.dictionary)
        self.atom_similarity_dist = _create_atom_similarity_distribution(
            vocab_size, self.gce_std_dev
        )
        logger.info(f"Initialized GCE loss with vocabulary size {vocab_size} and std_dev {self.gce_std_dev}")

        # Initialize complex pretraining components
        if self.complex_pretrain:
            self._init_distance_classification(task.args)

    def _init_distance_classification(self, args):
        """Initialize distance classification intervals and loss function."""
        self.dis_cls_lst = self._create_distance_intervals()

        # Validate number of distance classes
        expected_classes = getattr(args, 'dis_clsnum', 61)
        if len(self.dis_cls_lst) != expected_classes:
            raise ValueError(
                f"Expected {expected_classes} distance classes, "
                f"got {len(self.dis_cls_lst)}"
            )

        self.dis_cls_loss = nn.CrossEntropyLoss()

    def _create_distance_intervals(self) -> List[List[float]]:
        """
        Create distance classification intervals.

        Returns:
            List of [min, max] distance intervals for classification
        """
        intervals = []

        # Fine-grained intervals for distances 0-10 Å
        fine_range = np.arange(0, 10, DISTANCE_INTERVAL_FINE)
        for v in fine_range:
            intervals.append([v, v + DISTANCE_INTERVAL_FINE])

        # Coarse intervals for distances 10-20 Å
        coarse_range = np.arange(10, 20, DISTANCE_INTERVAL_COARSE)
        for v in coarse_range:
            intervals.append([v, v + DISTANCE_INTERVAL_COARSE])

        # Final interval for distances > 20 Å
        intervals.append([20, MAX_DISTANCE_THRESHOLD])

        return intervals

    def _compute_gce_masked_token_loss(
        self,
        logits_encoder: torch.Tensor,
        target: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute GCE (Generalized Cross-Entropy) loss for masked token prediction.
        """
        atom_similarity_dist = self.atom_similarity_dist.to(logits_encoder.device)

        vocab_size = logits_encoder.size(-1)
        one_hot = F.one_hot(target, num_classes=vocab_size).float()

        non_padding_mask = target.ne(self.padding_idx)

        if non_padding_mask.sum() == 0:
            return torch.tensor(0.0, device=logits_encoder.device, requires_grad=True)

        soft_labels = one_hot.clone()
        non_padding_indices = torch.nonzero(non_padding_mask, as_tuple=False).squeeze(-1)

        for idx in non_padding_indices:
            target_atom = target[idx].item()
            if target_atom > 0:  # Skip padding tokens
                # Use the pre-computed similarity distribution for this atom type
                soft_labels[idx] = atom_similarity_dist[target_atom]

        soft_labels = non_padding_mask.unsqueeze(-1).float() * soft_labels

        log_probs = F.log_softmax(logits_encoder, dim=-1)

        gce_loss = -torch.sum(soft_labels * log_probs)

        num_targets = non_padding_mask.long().sum().item()
        if num_targets > 0:
            gce_loss = gce_loss / num_targets

        return gce_loss

    def _compute_protein_ligand_contrastive_loss(
        self,
        protein_repr: torch.Tensor,
        ligand_repr: torch.Tensor,
    ) -> torch.Tensor:
        """
        Compute protein-ligand contrastive loss using InfoNCE.
        """
        batch_size = protein_repr.size(0)

        if batch_size < 2:
            return torch.tensor(0.0, device=protein_repr.device, requires_grad=True)

        # Normalize representations for cosine similarity
        protein_repr = F.normalize(protein_repr, p=2, dim=-1)
        ligand_repr = F.normalize(ligand_repr, p=2, dim=-1)

        # Compute similarity matrix: [batch_size, batch_size]
        # sim_matrix[i,j] = similarity between protein_i and ligand_j
        sim_matrix = torch.matmul(protein_repr, ligand_repr.transpose(0, 1))

        # Create positive pair mask (diagonal elements are positive pairs)
        positive_mask = torch.eye(batch_size, device=protein_repr.device, dtype=torch.bool)

        # Compute InfoNCE loss for protein-to-ligand direction
        protein_to_ligand_loss = self._compute_infonce_loss(sim_matrix, positive_mask)

        # Compute InfoNCE loss for ligand-to-protein direction (symmetric)
        ligand_to_protein_loss = self._compute_infonce_loss(sim_matrix.transpose(0, 1), positive_mask)

        # Average bidirectional losses
        contrastive_loss = (protein_to_ligand_loss + ligand_to_protein_loss) / 2.0

        return contrastive_loss

    def _compute_infonce_loss(
        self,
        sim_matrix: torch.Tensor,
        positive_mask: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute InfoNCE loss given similarity matrix and positive mask.

        Args:
            sim_matrix: Similarity matrix [N, N]
            positive_mask: Boolean mask indicating positive pairs [N, N]

        Returns:
            InfoNCE loss value
        """
        # Extract positive similarities
        positive_sim = sim_matrix[positive_mask]

        # Compute log-sum-exp for normalization (logsumexp for numerical stability)
        log_sum_exp = torch.logsumexp(sim_matrix, dim=1)

        # InfoNCE loss: -log(exp(positive_sim) / sum(exp(all_sim)))
        # = -positive_sim + log_sum_exp
        infonce_loss = -positive_sim + log_sum_exp

        return infonce_loss.mean()

    def _aggregate_representations(
        self,
        repr_tensor: torch.Tensor,
        mask: torch.Tensor
    ) -> torch.Tensor:
        """
        Aggregate sequence representations to single vector using masked mean pooling.

        Args:
            repr_tensor: Representations [batch_size, seq_len, embed_dim]
            mask: Valid position mask [batch_size, seq_len]

        Returns:
            Aggregated representations [batch_size, embed_dim]
        """
        # Expand mask to match representation dimensions
        mask_expanded = mask.unsqueeze(-1).float()  # [batch_size, seq_len, 1]

        # Masked sum
        masked_sum = (repr_tensor * mask_expanded).sum(dim=1)  # [batch_size, embed_dim]

        # Count valid positions
        valid_counts = mask.sum(dim=1, keepdim=True).float()  # [batch_size, 1]
        valid_counts = torch.clamp(valid_counts, min=1.0)  # Avoid division by zero

        # Masked mean
        aggregated = masked_sum / valid_counts

        return aggregated


    def forward(
        self,
        model,
        sample: Dict[str, Any],
        reduce: bool = True
    ) -> Tuple[torch.Tensor, int, Dict[str, Any]]:
        """
        Compute loss for the given sample.

        Args:
            model: UniMol model instance
            sample: Input sample containing net_input and target
            reduce: Whether to reduce the loss (unused, kept for compatibility)

        Returns:
            Tuple of (loss, sample_size, logging_output)
        """
        input_key = "net_input"
        target_key = "target"

        # Determine masked tokens for standard pretraining
        masked_tokens = None
        sample_size = 1

        if "tokens_target" in sample[target_key]:
            masked_tokens = sample[target_key]["tokens_target"].ne(self.padding_idx)
            sample_size = masked_tokens.long().sum()

        # Get model outputs based on pretraining mode
        if self.complex_pretrain:
            model_outputs = self._get_complex_model_outputs(model, sample[input_key])
            loss, logging_output = self._compute_complex_loss(model_outputs, sample)
        else:
            model_outputs = self._get_standard_model_outputs(
                model, sample[input_key], masked_tokens
            )
            loss, logging_output = self._compute_standard_loss(
                model_outputs, sample, masked_tokens, sample_size
            )

        return loss, sample_size, logging_output

    def _get_complex_model_outputs(self, model, net_input: Dict[str, Any]) -> Dict[str, Any]:
        """Get model outputs for complex pretraining mode."""
        if self.mask_feature:
            (all_feat_x, all_padding_mask, dis_cls_logits,
             x_norm, delta_encoder_pair_rep_norm, mask_pred_target_feat, mask_sovent_pred_target_feat) = model(**net_input)
            return {
                'all_feat_x': all_feat_x,
                'all_padding_mask': all_padding_mask,
                'dis_cls_logits': dis_cls_logits,
                'x_norm': x_norm,
                'delta_encoder_pair_rep_norm': delta_encoder_pair_rep_norm,
                'mask_pred_target_feat': mask_pred_target_feat,
                'mask_sovent_pred_target_feat': mask_sovent_pred_target_feat,
                'encoder_coord': None,
                'encoder_distance': None,
                'solvent_lig_coord': None
            }
        else:
            (all_feat_x, all_padding_mask, dis_cls_logits,
             x_norm, delta_encoder_pair_rep_norm) = model(**net_input)
            return {
                'all_feat_x': all_feat_x,
                'all_padding_mask': all_padding_mask,
                'dis_cls_logits': dis_cls_logits,
                'x_norm': x_norm,
                'delta_encoder_pair_rep_norm': delta_encoder_pair_rep_norm,
                'encoder_coord': None,
                'encoder_distance': None,
                'solvent_lig_coord': None
            }

    def _get_standard_model_outputs(
        self,
        model,
        net_input: Dict[str, Any],
        masked_tokens: torch.Tensor
    ) -> Dict[str, Any]:
        """Get model outputs for standard pretraining mode."""
        (logits_encoder, encoder_distance, encoder_coord, solvent_lig_coord, x_norm, delta_encoder_pair_rep_norm) = model(
            **net_input, encoder_masked_tokens=masked_tokens
        )

        return {
            'logits_encoder': logits_encoder,
            'encoder_distance': encoder_distance,
            'encoder_coord': encoder_coord,
            'solvent_lig_coord': solvent_lig_coord,
            # 'solvent_lig_coord_predict': solvent_lig_coord_predict,
            'x_norm': x_norm,
            'delta_encoder_pair_rep_norm': delta_encoder_pair_rep_norm
        }


    def _compute_kl_distance_loss(
        self,
        lig_proc_distance: torch.Tensor,
        lig_proc_distance_pred: torch.Tensor,
    ) -> torch.Tensor:
        """
        Compute KL divergence-based distance loss with improved numerical stability.
        """
        # Create distance mask (exclude padding and apply threshold)
        distance_mask = lig_proc_distance.ne(0)  # 0 is padding
        if hasattr(self.args, 'dist_threshold') and self.args.dist_threshold > 0:
            distance_mask &= (lig_proc_distance < self.args.dist_threshold)

        if distance_mask.sum() == 0:
            return torch.tensor(0.0, device=lig_proc_distance.device, requires_grad=True)

        distance_predict = lig_proc_distance_pred[distance_mask]
        distance_target = lig_proc_distance[distance_mask]

        # Clamp predictions to reasonable range to avoid extreme values
        distance_predict = torch.clamp(distance_predict, min=0.1, max=50.0)
        distance_target = torch.clamp(distance_target, min=0.1, max=50.0)

        # Use more reasonable and similar standard deviations
        # Adaptive std based on the scale of distances
        base_std = 0.2  # Reduced from 0.5 and 0.1
        pred_std = torch.ones_like(distance_predict) * base_std
        target_std = torch.ones_like(distance_target) * base_std

        # Create normal distributions
        try:
            pred_dist = Normal(distance_predict, pred_std)
            target_dist = Normal(distance_target, target_std)

            # Compute symmetric KL divergence for better stability
            # KL_sym = 0.5 * [KL(pred || target) + KL(target || pred)]
            kl_forward = kl_divergence(pred_dist, target_dist)
            kl_backward = kl_divergence(target_dist, pred_dist)

            # Handle potential NaN/Inf values
            kl_forward = torch.where(torch.isfinite(kl_forward), kl_forward, torch.zeros_like(kl_forward))
            kl_backward = torch.where(torch.isfinite(kl_backward), kl_backward, torch.zeros_like(kl_backward))

            # Symmetric KL divergence
            kl_loss = 0.5 * (kl_forward + kl_backward)

            # Apply additional clipping to prevent extremely large values
            kl_loss = torch.clamp(kl_loss, max=10.0)

            return kl_loss.mean()

        except Exception as e:
            logger.warning(f"Error computing KL divergence loss: {e}, falling back to MSE")
            # Fallback to MSE loss if KL computation fails
            return F.mse_loss(distance_predict.float(), distance_target.float(), reduction="mean")

    def _compute_complex_loss(
        self,
        model_outputs: Dict[str, Any],
        sample: Dict[str, Any]
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """Compute loss for complex pretraining mode."""
        # Extract model outputs
        all_feat_x = model_outputs['all_feat_x']
        dis_cls_logits = model_outputs['dis_cls_logits']
        x_norm = model_outputs['x_norm']
        delta_encoder_pair_rep_norm = model_outputs['delta_encoder_pair_rep_norm']

        # Extract target information
        prot_num_lst = sample['target']['prot_num']
        lig_num_lst = sample['target']['lig_num']
        batch_size = sample['target']['all_pos'].shape[0]

        # Initialize logging output
        logging_output = {
            "sample_size": 1,
            "bsz": batch_size,
            "seq_len": all_feat_x.shape[1],
            "prot_max_len": prot_num_lst.max().item(),
            "lig_max_len": lig_num_lst.max().item()
        }

        # Compute distance loss
        distance_loss = self._compute_distance_loss(
            sample, dis_cls_logits, prot_num_lst, lig_num_lst, batch_size
        )

        # Log appropriate distance loss type
        logging_output["distance_loss_value"] = distance_loss
        loss = distance_loss

        # Add feature masking loss if enabled
        if self.mask_feature and 'mask_pred_target_feat' in model_outputs:
            mask_feat_loss = self._compute_mask_feature_loss(
                model_outputs['mask_pred_target_feat']
            )

            if 'mask_sovent_pred_target_feat' in model_outputs:
                mask_solvent_feat_loss = self._compute_mask_feature_loss(
                    model_outputs['mask_sovent_pred_target_feat']
                )
                mask_loss = (mask_feat_loss + mask_solvent_feat_loss) * 0.5
            else:
                mask_loss = mask_feat_loss
            logging_output["mask_feat_loss"] = mask_loss
            loss += mask_loss

        # Add contrastive learning loss
        contrastive_loss = self._compute_contrastive_loss(
            model_outputs, sample, prot_num_lst, lig_num_lst, batch_size
        )
        if contrastive_loss > 0:
            loss += contrastive_loss
            logging_output["contrastive_loss"] = contrastive_loss

        # Add regularization losses
        loss, logging_output = self._add_regularization_losses(
            loss, logging_output, x_norm, delta_encoder_pair_rep_norm
        )

        logging_output["loss"] = loss.data
        return loss, logging_output

    def _compute_standard_loss(
        self,
        model_outputs: Dict[str, Any],
        sample: Dict[str, Any],
        masked_tokens: torch.Tensor,
        sample_size: int
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """Compute loss for standard pretraining mode."""
        target_key = "target"
        logits_encoder = model_outputs['logits_encoder']
        encoder_coord = model_outputs['encoder_coord']
        encoder_distance = model_outputs['encoder_distance']
        solvent_lig_coord = model_outputs['solvent_lig_coord_target']
        # solvent_lig_coord_predict = model_outputs['solvent_lig_coord_predict']
        x_norm = model_outputs['x_norm']
        delta_encoder_pair_rep_norm = model_outputs['delta_encoder_pair_rep_norm']

        # Compute masked token loss using GCE loss for improved molecular representation
        target = sample[target_key]["tokens_target"]
        if masked_tokens is not None:
            target = target[masked_tokens]

        masked_token_loss = self._compute_gce_masked_token_loss(logits_encoder, target)

        # Compute accuracy metrics
        masked_pred = logits_encoder.argmax(dim=-1)
        masked_hit = (masked_pred == target).long().sum()

        loss = masked_token_loss * self.args.masked_token_loss

        # Initialize logging output with loss type information
        logging_output = {
            "sample_size": 1,
            "bsz": sample[target_key]["tokens_target"].size(0),
            "seq_len": (sample[target_key]["tokens_target"].size(1) *
                       sample[target_key]["tokens_target"].size(0)),
            "masked_token_loss": masked_token_loss.data,
            "masked_token_hit": masked_hit.data,
            "masked_token_cnt": sample_size,
        }

        # Add coordinate loss if available
        if encoder_coord is not None:
            coord_loss = self._compute_coordinate_loss(
                encoder_coord, sample[target_key]["coord_target"], masked_tokens
            )
            loss += coord_loss * self.args.masked_coord_loss
            logging_output["masked_coord_loss"] = coord_loss.data

        # Add distance loss if available
        if encoder_distance is not None:
            dist_loss = self.cal_dist_loss(
                sample, encoder_distance, masked_tokens, target_key, normalize=True
            )
            loss += dist_loss * self.args.masked_dist_loss
            logging_output["masked_dist_loss"] = dist_loss.data

        if solvent_lig_coord is not None:
            solvent_coord_loss = self._compute_coordinate_loss(
                solvent_lig_coord, sample[target_key]["solvent_lig_coord_target"], masked_tokens
            )
            loss += solvent_coord_loss * self.args.masked_coord_loss
            logging_output["solvent_coord_loss"] = solvent_coord_loss.data


        # if solvent_lig_coord_predict is not None:
        #     solvent_coord_loss = self._compute_coordinate_loss(
        #         solvent_lig_coord_predict, sample[target_key]["solvent_lig_coord_target"], masked_tokens
        #     )
        #     loss += solvent_coord_loss * self.args.masked_coord_loss
        #     logging_output["solvent_coord_predict_loss"] = solvent_coord_loss.data

        # Add regularization losses
        loss, logging_output = self._add_regularization_losses(
            loss, logging_output, x_norm, delta_encoder_pair_rep_norm
        )

        logging_output["loss"] = loss.data
        return loss, logging_output

    def _compute_distance_loss(
        self,
        sample: Dict[str, Any],
        dis_cls_logits: torch.Tensor,
        prot_num_lst: torch.Tensor,
        lig_num_lst: torch.Tensor,
        batch_size: int
    ) -> torch.Tensor:
        """Compute distance-based loss for protein-ligand interactions."""
        distance_loss_all = 0.0

        for idx in range(batch_size):
            # Extract positions and compute distances
            all_pos = sample['target']['all_pos'][idx]
            proc_num = prot_num_lst[idx].item()
            lig_num = lig_num_lst[idx].item()

            proc_pos = all_pos[:proc_num, :]
            lig_pos = all_pos[proc_num:(proc_num + lig_num), :]
            lig_proc_distance = torch.cdist(lig_pos, proc_pos)

            # Extract predictions
            lig_proc_distance_pred = dis_cls_logits[idx][1:1+lig_num, 1:1+proc_num]

            # distance_loss = self._compute_kl_distance_loss(
            #     lig_proc_distance, lig_proc_distance_pred
            # )

            distance_loss = self._compute_regression_distance_loss(
                lig_proc_distance, lig_proc_distance_pred
            )

            if torch.isnan(distance_loss).any():
                logger.warning("Distance loss contains NaN values, setting to 0")
                distance_loss = torch.tensor(0.0, device=distance_loss.device)

            distance_loss_all += distance_loss

        return distance_loss_all / batch_size

    def _compute_contrastive_loss(
        self,
        model_outputs: Dict[str, Any],
        sample: Dict[str, Any],
        prot_num_lst: torch.Tensor,
        lig_num_lst: torch.Tensor,
        batch_size: int
    ) -> torch.Tensor:
        """
        Compute contrastive learning loss for protein-ligand binding.

        This method extracts protein and ligand representations and applies
        the appropriate contrastive learning strategy based on the configured mode.

        Args:
            model_outputs: Model outputs containing feature representations
            sample: Input sample with target information
            prot_num_lst: Number of protein atoms per sample
            lig_num_lst: Number of ligand atoms per sample
            batch_size: Batch size

        Returns:
            Computed contrastive loss
        """
        try:
            all_feat_x = model_outputs['all_feat_x']  # [batch_size, max_seq_len, embed_dim]
            all_padding_mask = model_outputs['all_padding_mask']  # [batch_size, max_seq_len]

            # Extract protein and ligand representations
            protein_reprs = []
            ligand_reprs = []

            for idx in range(batch_size):
                prot_num = prot_num_lst[idx].item()
                lig_num = lig_num_lst[idx].item()

                # Extract protein features (first prot_num tokens)
                prot_feat = all_feat_x[idx, :prot_num, :]  # [prot_num, embed_dim]
                prot_mask = ~all_padding_mask[idx, :prot_num]  # [prot_num]

                # Extract ligand features (next lig_num tokens after protein)
                lig_start = prot_num
                lig_end = prot_num + lig_num
                lig_feat = all_feat_x[idx, lig_start:lig_end, :]  # [lig_num, embed_dim]
                lig_mask = ~all_padding_mask[idx, lig_start:lig_end]  # [lig_num]

                # Aggregate to single representations using masked mean pooling
                prot_repr = self._aggregate_representations(
                    prot_feat.unsqueeze(0), prot_mask.unsqueeze(0)
                ).squeeze(0)  # [embed_dim]

                lig_repr = self._aggregate_representations(
                    lig_feat.unsqueeze(0), lig_mask.unsqueeze(0)
                ).squeeze(0)  # [embed_dim]

                protein_reprs.append(prot_repr)
                ligand_reprs.append(lig_repr)

            protein_reprs = torch.stack(protein_reprs, dim=0)  # [batch_size, embed_dim]
            ligand_reprs = torch.stack(ligand_reprs, dim=0)  # [batch_size, embed_dim]

            contrastive_loss = self._compute_protein_ligand_contrastive_loss(
                protein_reprs, ligand_reprs
            )

            return contrastive_loss

        except Exception as e:
            logger.warning(f"Error computing contrastive loss: {e}")
            return torch.tensor(0.0, device=all_feat_x.device, requires_grad=True)


    def _compute_regression_distance_loss(
        self,
        lig_proc_distance: torch.Tensor,
        lig_proc_distance_pred: torch.Tensor
    ) -> torch.Tensor:
        """Compute regression-based distance loss."""
        # Create distance mask (exclude padding and apply threshold)
        distance_mask = lig_proc_distance.ne(0)  # 0 is padding

        if hasattr(self.args, 'dist_threshold') and self.args.dist_threshold > 0:
            distance_mask &= (lig_proc_distance < self.args.dist_threshold)

        if distance_mask.sum() == 0:
            return torch.tensor(0.0, device=lig_proc_distance.device)

        distance_predict = lig_proc_distance_pred[distance_mask]
        distance_target = lig_proc_distance[distance_mask]

        return F.mse_loss(
            distance_predict.float(),
            distance_target.float(),
            reduction="mean"
        )

    def _compute_mask_feature_loss(self, mask_pred_target_feat: Tuple[torch.Tensor, torch.Tensor]) -> torch.Tensor:
        """Compute feature masking loss."""
        pred_feat, target_feat = mask_pred_target_feat
        return F.mse_loss(
            pred_feat.float(),
            target_feat.float(),
            reduction="mean"
        )

    def _compute_coordinate_loss(
        self,
        encoder_coord: torch.Tensor,
        coord_target: torch.Tensor,
        masked_tokens: torch.Tensor
    ) -> torch.Tensor:
        """Compute coordinate prediction loss."""
        return F.smooth_l1_loss(
            encoder_coord[masked_tokens].view(-1, 3).float(),
            coord_target[masked_tokens].view(-1, 3),
            reduction="mean",
            beta=1.0,
        )

    def _add_regularization_losses(
        self,
        loss: torch.Tensor,
        logging_output: Dict[str, Any],
        x_norm: Optional[torch.Tensor],
        delta_encoder_pair_rep_norm: Optional[torch.Tensor]
    ) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """Add regularization losses to the main loss."""
        # Add x norm loss
        if (hasattr(self.args, 'x_norm_loss') and
            self.args.x_norm_loss > 0 and
            x_norm is not None):
            loss = loss + self.args.x_norm_loss * x_norm
            logging_output["x_norm_loss"] = x_norm.data

        # Add delta pair representation norm loss
        if (hasattr(self.args, 'delta_pair_repr_norm_loss') and
            self.args.delta_pair_repr_norm_loss > 0 and
            delta_encoder_pair_rep_norm is not None):
            loss = loss + self.args.delta_pair_repr_norm_loss * delta_encoder_pair_rep_norm
            logging_output["delta_pair_repr_norm_loss"] = delta_encoder_pair_rep_norm.data

        return loss, logging_output

    @staticmethod
    def reduce_metrics(logging_outputs: List[Dict[str, Any]], split: str = "valid") -> None:
        """
        Aggregate logging outputs from data parallel training.

        This method collects and aggregates metrics from multiple workers
        in distributed training, computing averages and logging them for
        monitoring and evaluation purposes.

        Args:
            logging_outputs: List of logging dictionaries from each worker
            split: Dataset split name (train/valid/test)
        """
        if not logging_outputs:
            return

        # Aggregate basic metrics
        loss_sum = sum(log.get("loss", 0) for log in logging_outputs)
        bsz = sum(log.get("bsz", 0) for log in logging_outputs)
        sample_size = sum(log.get("sample_size", 0) for log in logging_outputs)
        seq_len = sum(log.get("seq_len", 0) for log in logging_outputs)

        if sample_size > 0:
            metrics.log_scalar("loss", loss_sum / sample_size, sample_size, round=3)
        if bsz > 0:
            metrics.log_scalar("seq_len", seq_len / bsz, 1, round=3)

        # Process masked token loss and accuracy
        UniMolLoss._log_masked_token_metrics(logging_outputs, sample_size)

        # Process other loss components
        UniMolLoss._log_coordinate_metrics(logging_outputs, sample_size)
        UniMolLoss._log_distance_metrics(logging_outputs, sample_size)
        UniMolLoss._log_regularization_metrics(logging_outputs, sample_size)
        UniMolLoss._log_complex_metrics(logging_outputs, sample_size)

    @staticmethod
    def _log_masked_token_metrics(logging_outputs: List[Dict[str, Any]], sample_size: int):
        """Log masked token loss and accuracy metrics."""
        masked_loss = sum(log.get("masked_token_loss", 0) for log in logging_outputs)
        if masked_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "masked_token_loss", masked_loss / sample_size, sample_size, round=3
            )
            UniMolLoss._wandb_log({"masked_token_loss": masked_loss / sample_size})

            # Calculate masked token accuracy
            total_hits = sum(log.get("masked_token_hit", 0) for log in logging_outputs)
            total_cnt = sum(log.get("masked_token_cnt", 0) for log in logging_outputs)

            if total_cnt > 0:
                masked_acc = total_hits / total_cnt
                metrics.log_scalar("masked_acc", masked_acc, sample_size, round=3)

    @staticmethod
    def _log_coordinate_metrics(logging_outputs: List[Dict[str, Any]], sample_size: int):
        """Log coordinate-related metrics."""
        masked_coord_loss = sum(log.get("masked_coord_loss", 0) for log in logging_outputs)
        if masked_coord_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "masked_coord_loss",
                masked_coord_loss / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({"masked_coord_loss": masked_coord_loss / sample_size})

    @staticmethod
    def _log_distance_metrics(logging_outputs: List[Dict[str, Any]], sample_size: int):
        """Log distance-related metrics."""
        masked_dist_loss = sum(log.get("masked_dist_loss", 0) for log in logging_outputs)
        if masked_dist_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "masked_dist_loss", masked_dist_loss / sample_size, sample_size, round=3
            )
            UniMolLoss._wandb_log({"masked_dist_loss": masked_dist_loss / sample_size})

    @staticmethod
    def _log_regularization_metrics(logging_outputs: List[Dict[str, Any]], sample_size: int):
        """Log regularization loss metrics."""
        # X norm loss
        x_norm_loss = sum(log.get("x_norm_loss", 0) for log in logging_outputs)
        if x_norm_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "x_norm_loss", x_norm_loss / sample_size, sample_size, round=3
            )
            UniMolLoss._wandb_log({"x_norm_loss": x_norm_loss / sample_size})

        # Delta pair representation norm loss
        delta_pair_repr_norm_loss = sum(
            log.get("delta_pair_repr_norm_loss", 0) for log in logging_outputs
        )
        if delta_pair_repr_norm_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "delta_pair_repr_norm_loss",
                delta_pair_repr_norm_loss / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({
                "delta_pair_repr_norm_loss": delta_pair_repr_norm_loss / sample_size
            })

    @staticmethod
    def _log_complex_metrics(logging_outputs: List[Dict[str, Any]], sample_size: int):
        """Log complex pretraining specific metrics."""
        # Distance classification loss
        dis_cls_loss_value = sum(log.get("dis_cls_loss_value", 0) for log in logging_outputs)
        if dis_cls_loss_value > 0 and sample_size > 0:
            metrics.log_scalar(
                "dis_cls_loss_value",
                dis_cls_loss_value / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({"dis_cls_loss_value": dis_cls_loss_value / sample_size})

        # Distance regression cross loss
        dis_reg_cross_loss_value = sum(
            log.get("dis_reg_cross_loss_value", 0) for log in logging_outputs
        )
        if dis_reg_cross_loss_value > 0 and sample_size > 0:
            metrics.log_scalar(
                "dis_reg_cross_loss_value",
                dis_reg_cross_loss_value / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({
                "dis_reg_cross_loss_value": dis_reg_cross_loss_value / sample_size
            })

        # KL distance loss
        distance_loss_value = sum(
            log.get("distance_loss_value", 0) for log in logging_outputs
        )
        if distance_loss_value > 0 and sample_size > 0:
            metrics.log_scalar(
                "distance_loss_value",
                distance_loss_value / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({
                "distance_loss_value": distance_loss_value / sample_size
            })

        # Mask feature loss
        mask_feat_loss = sum(log.get("mask_feat_loss", 0) for log in logging_outputs)
        if mask_feat_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "mask_feat_loss",
                mask_feat_loss / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({"mask_feat_loss": mask_feat_loss / sample_size})

        # Contrastive learning loss
        contrastive_loss = sum(log.get("contrastive_loss", 0) for log in logging_outputs)
        if contrastive_loss > 0 and sample_size > 0:
            metrics.log_scalar(
                "contrastive_loss",
                contrastive_loss / sample_size,
                sample_size,
                round=3,
            )
            UniMolLoss._wandb_log({"contrastive_loss": contrastive_loss / sample_size})

    @staticmethod
    def _wandb_log(log_dict: Dict[str, float]):
        try:
            if _get_local_rank() == 0:
                import wandb
                wandb.log(log_dict)
        except Exception as e:
            logger.debug(f"Failed to log to wandb: {e}")

    @staticmethod
    def logging_outputs_can_be_summed(is_train: bool) -> bool:
        """
        Whether the logging outputs returned by `forward` can be summed
        across workers prior to calling `reduce_metrics`.

        Setting this to True improves distributed training speed by allowing
        metrics aggregation before the reduce step.

        Args:
            is_train: Whether in training mode (unused but kept for compatibility)

        Returns:
            True to enable output summing across workers
        """
        return True

    def cal_dist_loss(
        self,
        sample: Dict[str, Any],
        dist: torch.Tensor,
        masked_tokens: torch.Tensor,
        target_key: str,
        normalize: bool = False
    ) -> torch.Tensor:
        """
        Calculate distance loss for masked positions.

        This method computes the loss between predicted and target distances
        for masked atom positions, with optional normalization.

        Args:
            sample: Input sample containing target distances
            dist: Predicted distance matrix
            masked_tokens: Boolean mask for masked positions
            target_key: Key for target data in sample
            normalize: Whether to normalize distances using mean/std

        Returns:
            Computed distance loss
        """
        try:
            # Extract masked distances and targets
            masked_distance = dist[masked_tokens, :]
            masked_distance_target = sample[target_key]["distance_target"][masked_tokens]

            # Filter out padding positions
            non_pad_pos = masked_distance_target > 0

            if non_pad_pos.sum() == 0:
                return torch.tensor(0.0, device=dist.device)

            # Apply normalization if requested
            if normalize:
                masked_distance_target = (
                    masked_distance_target.float() - self.dist_mean
                ) / self.dist_std

            # Compute smooth L1 loss
            masked_dist_loss = F.smooth_l1_loss(
                masked_distance[non_pad_pos].view(-1).float(),
                masked_distance_target[non_pad_pos].view(-1),
                reduction="mean",
                beta=1.0,
            )

            return masked_dist_loss

        except Exception as e:
            logger.error(f"Error computing distance loss: {e}")
            return torch.tensor(0.0, device=dist.device)


@register_loss("unimol_infer")
class UniMolInferLoss(UnicoreLoss):
    """
    Inference-specific loss function for UniMol model.

    This class extracts molecular representations and pair representations
    for downstream tasks during inference. It returns zero loss since
    no actual loss computation is needed during feature extraction.

    Attributes:
        padding_idx: Padding token index from the task dictionary
    """

    def __init__(self, task):
        super().__init__(task)
        self.padding_idx = task.dictionary.pad()

    def forward(
        self,
        model,
        sample: Dict[str, Any],
        reduce: bool = True
    ) -> Tuple[int, int, Dict[str, Any]]:
        """
        Extract molecular representations for inference.

        Args:
            model: UniMol model instance
            sample: Input sample containing net_input and target
            reduce: Whether to reduce the loss (unused, kept for compatibility)

        Returns:
            Tuple of (loss=0, sample_size, logging_output with representations)
        """
        try:
            input_key = "net_input"
            target_key = "target"

            # Get non-padding token mask
            src_tokens = sample[input_key]["src_tokens"].ne(self.padding_idx)

            # Extract features from model
            encoder_rep, encoder_pair_rep = model(
                **sample[input_key], features_only=True
            )

            sample_size = sample[input_key]["src_tokens"].size(0)

            # Process pair representations by removing padding
            encoder_pair_rep_list = self._process_pair_representations(
                encoder_pair_rep, src_tokens, sample_size
            )

            # Prepare logging output with extracted features
            logging_output = {
                "mol_repr_cls": encoder_rep[:, 0, :].data.cpu().numpy(),  # CLS token
                "pair_repr": encoder_pair_rep_list,
                "smi_name": sample[target_key]["smi_name"],
                "bsz": sample_size,
            }

            return 0, sample_size, logging_output

        except Exception as e:
            logger.error(f"Error in UniMolInferLoss forward pass: {e}")
            # Return minimal logging output on error
            return 0, 1, {"mol_repr_cls": None, "pair_repr": [], "smi_name": [], "bsz": 1}

    def _process_pair_representations(
        self,
        encoder_pair_rep: torch.Tensor,
        src_tokens: torch.Tensor,
        sample_size: int
    ) -> List[np.ndarray]:
        """
        Process pair representations by removing padding tokens.

        Args:
            encoder_pair_rep: Raw pair representations from encoder
            src_tokens: Boolean mask for non-padding tokens
            sample_size: Number of samples in batch

        Returns:
            List of processed pair representation arrays
        """
        encoder_pair_rep_list = []

        for i in range(sample_size):
            try:
                # Extract non-padding positions for sample i
                valid_positions = src_tokens[i]

                # Remove padding from both dimensions
                processed_repr = encoder_pair_rep[i][valid_positions, :][:, valid_positions]
                encoder_pair_rep_list.append(processed_repr.data.cpu().numpy())

            except Exception as e:
                logger.warning(f"Error processing pair representation for sample {i}: {e}")
                # Add empty array as fallback
                encoder_pair_rep_list.append(np.array([]))

        return encoder_pair_rep_list

    @staticmethod
    def reduce_metrics(logging_outputs: List[Dict[str, Any]]) -> None:
        """
        Aggregate logging outputs from data parallel training.

        For inference, we typically don't need to reduce metrics,
        but this method is kept for compatibility.

        Args:
            logging_outputs: List of logging dictionaries from each worker
        """
        if not logging_outputs:
            return

        # Log basic batch size information
        total_bsz = sum(log.get("bsz", 0) for log in logging_outputs)
        if total_bsz > 0:
            metrics.log_scalar("inference_samples", total_bsz, 1, round=0)

    @staticmethod
    def logging_outputs_can_be_summed() -> bool:
        """
        Whether the logging outputs returned by `forward` can be summed
        across workers prior to calling `reduce_metrics`.

        For inference with complex outputs like representations,
        we typically don't want to sum outputs.

        Returns:
            False to prevent output summing across workers
        """
        return False
